#!/bin/bash

# Cyber POS System - Setup Script
# This script helps with initial setup and deployment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check Node.js version
check_node_version() {
    if command_exists node; then
        NODE_VERSION=$(node --version | cut -d'v' -f2)
        REQUIRED_VERSION="18.0.0"
        
        if [ "$(printf '%s\n' "$REQUIRED_VERSION" "$NODE_VERSION" | sort -V | head -n1)" = "$REQUIRED_VERSION" ]; then
            print_success "Node.js version $NODE_VERSION is compatible"
        else
            print_error "Node.js version $NODE_VERSION is too old. Required: $REQUIRED_VERSION or higher"
            exit 1
        fi
    else
        print_error "Node.js is not installed. Please install Node.js $REQUIRED_VERSION or higher"
        exit 1
    fi
}

# Function to check npm version
check_npm_version() {
    if command_exists npm; then
        NPM_VERSION=$(npm --version)
        print_success "npm version $NPM_VERSION found"
    else
        print_error "npm is not installed"
        exit 1
    fi
}

# Function to install dependencies
install_dependencies() {
    print_status "Installing project dependencies..."
    
    if [ -f "package.json" ]; then
        npm install
        print_success "Dependencies installed successfully"
    else
        print_error "package.json not found. Are you in the correct directory?"
        exit 1
    fi
}

# Function to install Firebase CLI
install_firebase_cli() {
    if command_exists firebase; then
        FIREBASE_VERSION=$(firebase --version)
        print_success "Firebase CLI already installed: $FIREBASE_VERSION"
    else
        print_status "Installing Firebase CLI..."
        npm install -g firebase-tools
        print_success "Firebase CLI installed successfully"
    fi
}

# Function to setup environment variables
setup_environment() {
    print_status "Setting up environment variables..."
    
    if [ ! -f ".env.local" ]; then
        print_warning ".env.local not found. Creating template..."
        cat > .env.local << EOF
# Firebase Configuration
REACT_APP_FIREBASE_API_KEY=your_api_key_here
REACT_APP_FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
REACT_APP_FIREBASE_PROJECT_ID=your_project_id
REACT_APP_FIREBASE_STORAGE_BUCKET=your_project.appspot.com
REACT_APP_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
REACT_APP_FIREBASE_APP_ID=your_app_id

# Optional: Analytics
REACT_APP_FIREBASE_MEASUREMENT_ID=your_measurement_id

# Environment
REACT_APP_ENVIRONMENT=development
EOF
        print_warning "Please update .env.local with your Firebase configuration"
        print_warning "You can find these values in your Firebase project settings"
    else
        print_success "Environment file already exists"
    fi
}

# Function to setup Firebase project
setup_firebase() {
    print_status "Setting up Firebase project..."
    
    if [ ! -f "firebase.json" ]; then
        print_status "Initializing Firebase project..."
        firebase init
        print_success "Firebase project initialized"
    else
        print_success "Firebase project already initialized"
    fi
}

# Function to build the project
build_project() {
    print_status "Building the project..."
    
    npm run build
    
    if [ $? -eq 0 ]; then
        print_success "Project built successfully"
    else
        print_error "Build failed. Please check the errors above"
        exit 1
    fi
}

# Function to run tests
run_tests() {
    print_status "Running tests..."
    
    npm test -- --coverage --watchAll=false
    
    if [ $? -eq 0 ]; then
        print_success "All tests passed"
    else
        print_warning "Some tests failed. Please review and fix before deployment"
    fi
}

# Function to deploy to Firebase
deploy_firebase() {
    print_status "Deploying to Firebase..."
    
    firebase deploy
    
    if [ $? -eq 0 ]; then
        print_success "Deployment successful!"
        print_status "Your app is now live at your Firebase hosting URL"
    else
        print_error "Deployment failed. Please check the errors above"
        exit 1
    fi
}

# Function to setup development environment
setup_development() {
    print_status "Setting up development environment..."
    
    check_node_version
    check_npm_version
    install_dependencies
    install_firebase_cli
    setup_environment
    
    print_success "Development environment setup complete!"
    print_status "Next steps:"
    echo "1. Update .env.local with your Firebase configuration"
    echo "2. Run 'npm start' to start the development server"
    echo "3. Run 'npm test' to run tests"
}

# Function to setup production deployment
setup_production() {
    print_status "Setting up production deployment..."
    
    check_node_version
    check_npm_version
    install_dependencies
    install_firebase_cli
    setup_firebase
    run_tests
    build_project
    
    read -p "Do you want to deploy to Firebase now? (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        deploy_firebase
    else
        print_status "Build complete. Run 'firebase deploy' when ready to deploy"
    fi
}

# Function to create initial admin user
create_admin_user() {
    print_status "Creating initial admin user..."
    
    read -p "Enter admin email: " ADMIN_EMAIL
    read -s -p "Enter admin password: " ADMIN_PASSWORD
    echo
    
    # This would typically be done through Firebase Admin SDK
    # For now, we'll provide instructions
    print_status "To create the admin user:"
    echo "1. Go to Firebase Console > Authentication > Users"
    echo "2. Add user with email: $ADMIN_EMAIL"
    echo "3. Set custom claims: {\"role\": \"admin\"}"
    echo "4. Or use the Firebase Admin SDK script (if available)"
}

# Function to show help
show_help() {
    echo "Cyber POS System Setup Script"
    echo ""
    echo "Usage: $0 [OPTION]"
    echo ""
    echo "Options:"
    echo "  dev         Setup development environment"
    echo "  prod        Setup production deployment"
    echo "  build       Build the project only"
    echo "  test        Run tests only"
    echo "  deploy      Deploy to Firebase only"
    echo "  admin       Create initial admin user"
    echo "  help        Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 dev      # Setup development environment"
    echo "  $0 prod     # Setup and deploy to production"
    echo "  $0 build    # Build project only"
}

# Main script logic
main() {
    echo "🚀 Cyber POS System Setup Script"
    echo "=================================="
    echo ""
    
    case "${1:-help}" in
        "dev")
            setup_development
            ;;
        "prod")
            setup_production
            ;;
        "build")
            check_node_version
            check_npm_version
            install_dependencies
            build_project
            ;;
        "test")
            check_node_version
            check_npm_version
            install_dependencies
            run_tests
            ;;
        "deploy")
            install_firebase_cli
            deploy_firebase
            ;;
        "admin")
            create_admin_user
            ;;
        "help"|*)
            show_help
            ;;
    esac
}

# Run main function with all arguments
main "$@"
