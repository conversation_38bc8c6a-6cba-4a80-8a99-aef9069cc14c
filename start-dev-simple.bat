@echo off
echo Starting Cyber POS Development Environment...
echo.

REM Check if Firebase CLI is installed
firebase --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Firebase CLI not found. Installing...
    npm install -g firebase-tools
    if %errorlevel% neq 0 (
        echo Failed to install Firebase CLI. Please install manually.
        pause
        exit /b 1
    )
)

REM Start Firebase emulators in background
echo Starting Firebase emulators...
start "Firebase Emulators" cmd /k "firebase emulators:start"

REM Wait for emulators to start
echo Waiting for Firebase emulators to start...
timeout /t 10 /nobreak >nul

REM Start React development server
echo Starting React development server...
cd cyber-pos-system
start "React Dev Server" cmd /k "npm start"

echo.
echo Development environment started!
echo.
echo Firebase Emulator UI: http://localhost:4000
echo React App: http://localhost:3000
echo.
echo Press any key to exit...
pause >nul
