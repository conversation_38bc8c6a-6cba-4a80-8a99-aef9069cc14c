# Troubleshooting Guide for Cyber POS System

## Issues Fixed

### 1. WebSocket Connection Failures
**Problem**: `WebSocket connection to 'ws://localhost:3000/ws' failed`

**Cause**: React development server not running properly

**Solution**: 
- Start the React development server: `cd cyber-pos-system && npm start`
- The WebSocket is used for hot reloading and should connect automatically once the server is running

### 2. Firebase Permission Errors
**Problem**: `FirebaseError: Missing or insufficient permissions` during demo data seeding

**Cause**: Firestore security rules were too restrictive for demo data seeding

**Solution Applied**: 
- Modified `firestore.rules` to temporarily allow all operations for development
- **IMPORTANT**: These permissive rules are for development only and should be reverted for production

## Current Status

✅ **Fixed**: Firestore security rules updated to allow demo data seeding
✅ **Fixed**: Created simplified startup script (`start-dev-simple.bat`)
✅ **Fixed**: Firebase configuration error resolved
✅ **Fixed**: TypeScript error in test utilities resolved
✅ **Updated**: Temporarily disabled emulator mode to use production Firebase
⚠️ **Note**: Currently using production Firebase instead of emulators

## How to Start the Development Environment

### Current Setup (Production Firebase)
Since emulators are having issues, we're currently using production Firebase:

```bash
# Start React app only (from cyber-pos-system directory)
cd cyber-pos-system
npm start
```

### Option 1: Use the Simple Startup Script (when emulators work)
```bash
# Run this from the root directory (E:\FX\Cyber POS)
start-dev-simple.bat
```

### Option 2: Manual Start with Emulators (when available)
```bash
# Terminal 1: Start Firebase emulators (from root directory)
firebase emulators:start

# Terminal 2: Start React app (from cyber-pos-system directory)
cd cyber-pos-system
npm start
```

### Option 3: Use Existing Scripts
```bash
# Windows
start-dev.bat

# Mac/Linux
./start-dev.sh
```

## Accessing the Application

- **React App**: http://localhost:3000
- **Firebase Emulator UI**: http://localhost:4000
- **Firestore Emulator**: localhost:8080
- **Auth Emulator**: localhost:9099
- **Storage Emulator**: localhost:9199

## Demo Data Seeding

Once the app is running:

1. Go to http://localhost:3000
2. You'll see the login page
3. Click "Initialize Demo Data" button to seed the database
4. Use these demo credentials:
   - **Admin**: <EMAIL> / password
   - **Attendant**: <EMAIL> / password
   - **Technician**: <EMAIL> / password

## Next Steps

1. **Test the Application**: 
   - Try logging in with demo credentials
   - Test POS functionality
   - Check inventory management
   - Generate reports

2. **Revert Security Rules for Production**:
   - The current `firestore.rules` file has permissive rules for development
   - Before deploying to production, restore proper authentication-based rules

3. **Install Firebase CLI** (if not already installed):
   ```bash
   npm install -g firebase-tools
   ```

## Common Issues

### Firebase CLI Not Found
```bash
npm install -g firebase-tools
```

### Port Already in Use
- Check if other applications are using ports 3000, 4000, 8080, 9099, or 9199
- Kill existing processes or change ports in configuration

### Demo Data Not Loading
- Ensure Firebase emulators are running
- Check browser console for errors
- Verify Firestore rules allow the operations

## Environment Variables

The app uses these environment variables (from `.env.local`):
- `REACT_APP_USE_FIREBASE_EMULATOR=true` (for development)
- Firebase configuration variables
- Emulator host configurations

## Support

If you encounter issues:
1. Check the browser console for errors
2. Check the terminal output for both React and Firebase processes
3. Verify all services are running on their expected ports
4. Ensure all dependencies are installed (`npm install`)
