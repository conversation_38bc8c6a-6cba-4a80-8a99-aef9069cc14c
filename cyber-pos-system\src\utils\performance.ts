// Performance monitoring and optimization utilities

interface PerformanceMetric {
  name: string;
  startTime: number;
  endTime?: number;
  duration?: number;
  metadata?: Record<string, any>;
}

class PerformanceMonitor {
  private metrics: Map<string, PerformanceMetric> = new Map();
  private observers: PerformanceObserver[] = [];

  constructor() {
    this.initializeObservers();
  }

  // Initialize performance observers
  private initializeObservers() {
    if (typeof window !== 'undefined' && 'PerformanceObserver' in window) {
      // Observe navigation timing
      const navObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (entry.entryType === 'navigation') {
            this.logNavigationMetrics(entry as PerformanceNavigationTiming);
          }
        }
      });
      navObserver.observe({ entryTypes: ['navigation'] });
      this.observers.push(navObserver);

      // Observe resource timing
      const resourceObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (entry.entryType === 'resource') {
            this.logResourceMetrics(entry as PerformanceResourceTiming);
          }
        }
      });
      resourceObserver.observe({ entryTypes: ['resource'] });
      this.observers.push(resourceObserver);

      // Observe largest contentful paint
      const lcpObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (entry.entryType === 'largest-contentful-paint') {
            this.logMetric('LCP', entry.startTime, { size: (entry as any).size });
          }
        }
      });
      lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });
      this.observers.push(lcpObserver);

      // Observe first input delay
      const fidObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (entry.entryType === 'first-input') {
            this.logMetric('FID', (entry as any).processingStart - entry.startTime);
          }
        }
      });
      fidObserver.observe({ entryTypes: ['first-input'] });
      this.observers.push(fidObserver);
    }
  }

  // Start timing a custom metric
  startTiming(name: string, metadata?: Record<string, any>): void {
    const startTime = performance.now();
    this.metrics.set(name, {
      name,
      startTime,
      metadata
    });
  }

  // End timing a custom metric
  endTiming(name: string): number | null {
    const metric = this.metrics.get(name);
    if (!metric) {
      console.warn(`Performance metric '${name}' not found`);
      return null;
    }

    const endTime = performance.now();
    const duration = endTime - metric.startTime;
    
    metric.endTime = endTime;
    metric.duration = duration;

    this.logMetric(name, duration, metric.metadata);
    this.metrics.delete(name);

    return duration;
  }

  // Log a performance metric
  public logMetric(name: string, value: number, metadata?: Record<string, any>): void {
    const metric = {
      name,
      value,
      timestamp: Date.now(),
      metadata
    };

    // Log to console in development
    if (process.env.NODE_ENV === 'development') {
      console.log(`Performance: ${name} = ${value.toFixed(2)}ms`, metadata);
    }

    // Send to analytics service in production
    if (process.env.NODE_ENV === 'production') {
      this.sendToAnalytics(metric);
    }
  }

  // Log navigation metrics
  private logNavigationMetrics(entry: PerformanceNavigationTiming): void {
    const metrics = {
      'DNS Lookup': entry.domainLookupEnd - entry.domainLookupStart,
      'TCP Connection': entry.connectEnd - entry.connectStart,
      'TLS Handshake': entry.secureConnectionStart > 0 ? entry.connectEnd - entry.secureConnectionStart : 0,
      'Request': entry.responseStart - entry.requestStart,
      'Response': entry.responseEnd - entry.responseStart,
      'DOM Processing': entry.domComplete - entry.domContentLoadedEventStart,
      'Load Complete': entry.loadEventEnd - entry.loadEventStart,
      'Total Load Time': entry.loadEventEnd - entry.fetchStart
    };

    Object.entries(metrics).forEach(([name, value]) => {
      if (value > 0) {
        this.logMetric(`Navigation: ${name}`, value);
      }
    });
  }

  // Log resource metrics
  private logResourceMetrics(entry: PerformanceResourceTiming): void {
    const duration = entry.responseEnd - entry.startTime;
    const resourceType = this.getResourceType(entry.name);
    
    // Only log slow resources (>100ms) to avoid noise
    if (duration > 100) {
      this.logMetric(`Resource: ${resourceType}`, duration, {
        url: entry.name,
        size: entry.transferSize
      });
    }
  }

  // Get resource type from URL
  private getResourceType(url: string): string {
    if (url.includes('.js')) return 'JavaScript';
    if (url.includes('.css')) return 'CSS';
    if (url.match(/\.(png|jpg|jpeg|gif|svg|webp)$/)) return 'Image';
    if (url.includes('font')) return 'Font';
    if (url.includes('api') || url.includes('firestore')) return 'API';
    return 'Other';
  }

  // Send metrics to analytics service
  private sendToAnalytics(metric: any): void {
    // Implementation would depend on your analytics service
    // Example: Google Analytics, Firebase Analytics, etc.
    console.log('Sending to analytics:', metric);
  }

  // Get all recorded metrics
  getMetrics(): PerformanceMetric[] {
    return Array.from(this.metrics.values());
  }

  // Clear all metrics
  clearMetrics(): void {
    this.metrics.clear();
  }

  // Cleanup observers
  disconnect(): void {
    this.observers.forEach(observer => observer.disconnect());
    this.observers = [];
  }
}

// Singleton instance
export const performanceMonitor = new PerformanceMonitor();

// Utility functions for common performance optimizations

// Debounce function to limit function calls
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

// Throttle function to limit function calls
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean;
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
}

// Memoization for expensive calculations
export function memoize<T extends (...args: any[]) => any>(
  func: T,
  getKey?: (...args: Parameters<T>) => string
): T {
  const cache = new Map();
  
  return ((...args: Parameters<T>) => {
    const key = getKey ? getKey(...args) : JSON.stringify(args);
    
    if (cache.has(key)) {
      return cache.get(key);
    }
    
    const result = func(...args);
    cache.set(key, result);
    return result;
  }) as T;
}

// Lazy loading utility
export function lazyLoad<T>(
  importFunc: () => Promise<T>
): () => Promise<T> {
  let promise: Promise<T> | null = null;
  
  return () => {
    if (!promise) {
      promise = importFunc();
    }
    return promise;
  };
}

// Image optimization utility
export function optimizeImage(
  src: string,
  width?: number,
  height?: number,
  quality: number = 80
): string {
  // This would integrate with an image optimization service
  // For now, return the original src
  return src;
}

// Bundle size analyzer
export function analyzeBundleSize(): void {
  if (process.env.NODE_ENV === 'development') {
    // This would integrate with webpack-bundle-analyzer or similar
    console.log('Bundle analysis would be performed here');
  }
}

// Memory usage monitor
export function monitorMemoryUsage(): void {
  if ('memory' in performance) {
    const memory = (performance as any).memory;
    performanceMonitor.logMetric('Memory: Used JS Heap', memory.usedJSHeapSize / 1024 / 1024);
    performanceMonitor.logMetric('Memory: Total JS Heap', memory.totalJSHeapSize / 1024 / 1024);
    performanceMonitor.logMetric('Memory: JS Heap Limit', memory.jsHeapSizeLimit / 1024 / 1024);
  }
}

// Network quality detection
export function detectNetworkQuality(): Promise<string> {
  return new Promise((resolve) => {
    if ('connection' in navigator) {
      const connection = (navigator as any).connection;
      const effectiveType = connection.effectiveType || 'unknown';
      resolve(effectiveType);
    } else {
      // Fallback: measure download speed
      const startTime = performance.now();
      const image = new Image();
      
      image.onload = () => {
        const duration = performance.now() - startTime;
        const quality = duration < 100 ? 'fast' : duration < 500 ? 'medium' : 'slow';
        resolve(quality);
      };
      
      image.onerror = () => resolve('unknown');
      image.src = '/favicon.ico?' + Math.random();
    }
  });
}

// Critical resource preloader
export function preloadCriticalResources(resources: string[]): void {
  resources.forEach(resource => {
    const link = document.createElement('link');
    link.rel = 'preload';
    link.href = resource;
    
    if (resource.endsWith('.js')) {
      link.as = 'script';
    } else if (resource.endsWith('.css')) {
      link.as = 'style';
    } else if (resource.match(/\.(woff|woff2|ttf|otf)$/)) {
      link.as = 'font';
      link.crossOrigin = 'anonymous';
    }
    
    document.head.appendChild(link);
  });
}

// Service worker registration with performance monitoring
export function registerServiceWorker(): void {
  if ('serviceWorker' in navigator) {
    performanceMonitor.startTiming('Service Worker Registration');
    
    navigator.serviceWorker.register('/sw.js')
      .then((registration) => {
        performanceMonitor.endTiming('Service Worker Registration');
        console.log('Service Worker registered successfully:', registration);
        
        // Monitor service worker updates
        registration.addEventListener('updatefound', () => {
          console.log('Service Worker update found');
        });
      })
      .catch((error) => {
        performanceMonitor.endTiming('Service Worker Registration');
        console.error('Service Worker registration failed:', error);
      });
  }
}

// Performance budget checker
export function checkPerformanceBudget(): void {
  const budgets = {
    'First Contentful Paint': 1500,
    'Largest Contentful Paint': 2500,
    'First Input Delay': 100,
    'Cumulative Layout Shift': 0.1
  };

  // This would check actual metrics against budgets
  // and alert if budgets are exceeded
  Object.entries(budgets).forEach(([metric, budget]) => {
    // Implementation would check actual values
    console.log(`Budget check: ${metric} should be under ${budget}`);
  });
}

// Initialize performance monitoring
export function initializePerformanceMonitoring(): void {
  // Register service worker
  registerServiceWorker();
  
  // Monitor memory usage periodically
  setInterval(monitorMemoryUsage, 30000);
  
  // Detect network quality
  detectNetworkQuality().then(quality => {
    performanceMonitor.logMetric('Network Quality', 0, { quality });
  });
  
  // Preload critical resources
  preloadCriticalResources([
    '/static/css/main.css',
    '/static/js/bundle.js'
  ]);
  
  // Check performance budget
  setTimeout(checkPerformanceBudget, 5000);
}
