import React, { useState } from 'react';
import { testFirebaseConnection, testServicesQuery } from '../../utils/firebaseConnectionTest';

interface TestResult {
  success: boolean;
  message: string;
  details?: any;
  servicesCount?: number;
}

const FirebaseConnectionTest: React.FC = () => {
  const [connectionResult, setConnectionResult] = useState<TestResult | null>(null);
  const [servicesResult, setServicesResult] = useState<TestResult | null>(null);
  const [testing, setTesting] = useState(false);

  const runConnectionTest = async () => {
    setTesting(true);
    try {
      const result = await testFirebaseConnection();
      setConnectionResult(result);
    } catch (error) {
      setConnectionResult({
        success: false,
        message: `Test failed: ${error}`,
      });
    }
    setTesting(false);
  };

  const runServicesTest = async () => {
    setTesting(true);
    try {
      const result = await testServicesQuery();
      setServicesResult(result);
    } catch (error) {
      setServicesResult({
        success: false,
        message: `Test failed: ${error}`,
      });
    }
    setTesting(false);
  };

  const runAllTests = async () => {
    await runConnectionTest();
    await runServicesTest();
  };

  return (
    <div className="p-6 bg-white rounded-lg shadow-md">
      <h2 className="text-xl font-bold mb-4">Firebase Connection Test</h2>
      
      <div className="space-y-4">
        <div className="flex space-x-2">
          <button
            onClick={runConnectionTest}
            disabled={testing}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
          >
            Test Connection
          </button>
          <button
            onClick={runServicesTest}
            disabled={testing}
            className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 disabled:opacity-50"
          >
            Test Services Query
          </button>
          <button
            onClick={runAllTests}
            disabled={testing}
            className="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600 disabled:opacity-50"
          >
            Run All Tests
          </button>
        </div>

        {testing && (
          <div className="text-blue-600">
            🔄 Running tests...
          </div>
        )}

        {connectionResult && (
          <div className={`p-4 rounded ${connectionResult.success ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
            <h3 className="font-semibold">Connection Test Result:</h3>
            <p>{connectionResult.message}</p>
            {connectionResult.details && (
              <pre className="mt-2 text-sm">
                {JSON.stringify(connectionResult.details, null, 2)}
              </pre>
            )}
          </div>
        )}

        {servicesResult && (
          <div className={`p-4 rounded ${servicesResult.success ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
            <h3 className="font-semibold">Services Query Test Result:</h3>
            <p>{servicesResult.message}</p>
            {servicesResult.servicesCount !== undefined && (
              <p className="mt-1">Services found: {servicesResult.servicesCount}</p>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default FirebaseConnectionTest;
