// Data validation utilities for security and data integrity

export interface ValidationRule {
  required?: boolean;
  type?: 'string' | 'number' | 'boolean' | 'email' | 'phone' | 'date' | 'array' | 'object';
  minLength?: number;
  maxLength?: number;
  min?: number;
  max?: number;
  pattern?: RegExp;
  custom?: (value: any) => boolean | string;
  sanitize?: boolean;
}

export interface ValidationSchema {
  [key: string]: ValidationRule;
}

export interface ValidationResult {
  isValid: boolean;
  errors: Record<string, string[]>;
  sanitizedData?: any;
}

// Sanitization functions
export const sanitize = {
  // Remove HTML tags and dangerous characters
  html: (input: string): string => {
    return input
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
      .replace(/<[^>]*>/g, '')
      .replace(/[<>'"&]/g, (char) => {
        const entities: Record<string, string> = {
          '<': '&lt;',
          '>': '&gt;',
          '"': '&quot;',
          "'": '&#x27;',
          '&': '&amp;'
        };
        return entities[char] || char;
      });
  },

  // Remove SQL injection patterns
  sql: (input: string): string => {
    const sqlPatterns = [
      /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION|SCRIPT)\b)/gi,
      /(--|\/\*|\*\/|;|'|")/g,
      /(\bOR\b|\bAND\b)\s+\d+\s*=\s*\d+/gi
    ];
    
    let sanitized = input;
    sqlPatterns.forEach(pattern => {
      sanitized = sanitized.replace(pattern, '');
    });
    
    return sanitized.trim();
  },

  // Remove XSS patterns
  xss: (input: string): string => {
    const xssPatterns = [
      /javascript:/gi,
      /vbscript:/gi,
      /onload/gi,
      /onerror/gi,
      /onclick/gi,
      /onmouseover/gi,
      /onfocus/gi,
      /onblur/gi,
      /onchange/gi,
      /onsubmit/gi
    ];
    
    let sanitized = input;
    xssPatterns.forEach(pattern => {
      sanitized = sanitized.replace(pattern, '');
    });
    
    return sanitized;
  },

  // Sanitize phone numbers
  phone: (input: string): string => {
    return input.replace(/[^\d+\-\s()]/g, '').trim();
  },

  // Sanitize email addresses
  email: (input: string): string => {
    return input.toLowerCase().trim();
  },

  // General text sanitization
  text: (input: string): string => {
    return sanitize.xss(sanitize.html(input)).trim();
  }
};

// Validation functions
export const validators = {
  required: (value: any): boolean => {
    if (value === null || value === undefined) return false;
    if (typeof value === 'string') return value.trim().length > 0;
    if (Array.isArray(value)) return value.length > 0;
    return true;
  },

  email: (value: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(value);
  },

  phone: (value: string): boolean => {
    // Kenyan phone number format
    const phoneRegex = /^(\+254|0)[17]\d{8}$/;
    return phoneRegex.test(value.replace(/[\s\-()]/g, ''));
  },

  strongPassword: (value: string): boolean => {
    // At least 8 characters, 1 uppercase, 1 lowercase, 1 number, 1 special char
    const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/;
    return passwordRegex.test(value);
  },

  currency: (value: number): boolean => {
    return typeof value === 'number' && value >= 0 && Number.isFinite(value);
  },

  positiveInteger: (value: number): boolean => {
    return Number.isInteger(value) && value >= 0;
  },

  dateString: (value: string): boolean => {
    const date = new Date(value);
    return !isNaN(date.getTime());
  },

  url: (value: string): boolean => {
    try {
      new URL(value);
      return true;
    } catch {
      return false;
    }
  },

  alphanumeric: (value: string): boolean => {
    return /^[a-zA-Z0-9]+$/.test(value);
  },

  noSpecialChars: (value: string): boolean => {
    return /^[a-zA-Z0-9\s\-_.,]+$/.test(value);
  }
};

// Main validation function
export function validateData(data: any, schema: ValidationSchema): ValidationResult {
  const errors: Record<string, string[]> = {};
  const sanitizedData: any = {};

  for (const [field, rule] of Object.entries(schema)) {
    const value = data[field];
    const fieldErrors: string[] = [];

    // Check required
    if (rule.required && !validators.required(value)) {
      fieldErrors.push(`${field} is required`);
      continue;
    }

    // Skip validation if field is not required and empty
    if (!rule.required && (!value || (typeof value === 'string' && value.trim() === ''))) {
      sanitizedData[field] = value;
      continue;
    }

    // Type validation
    if (rule.type) {
      switch (rule.type) {
        case 'string':
          if (typeof value !== 'string') {
            fieldErrors.push(`${field} must be a string`);
          }
          break;
        case 'number':
          if (typeof value !== 'number' || !Number.isFinite(value)) {
            fieldErrors.push(`${field} must be a valid number`);
          }
          break;
        case 'boolean':
          if (typeof value !== 'boolean') {
            fieldErrors.push(`${field} must be a boolean`);
          }
          break;
        case 'email':
          if (!validators.email(value)) {
            fieldErrors.push(`${field} must be a valid email address`);
          }
          break;
        case 'phone':
          if (!validators.phone(value)) {
            fieldErrors.push(`${field} must be a valid phone number`);
          }
          break;
        case 'date':
          if (!validators.dateString(value)) {
            fieldErrors.push(`${field} must be a valid date`);
          }
          break;
        case 'array':
          if (!Array.isArray(value)) {
            fieldErrors.push(`${field} must be an array`);
          }
          break;
        case 'object':
          if (typeof value !== 'object' || Array.isArray(value) || value === null) {
            fieldErrors.push(`${field} must be an object`);
          }
          break;
      }
    }

    // Length validation for strings
    if (typeof value === 'string') {
      if (rule.minLength && value.length < rule.minLength) {
        fieldErrors.push(`${field} must be at least ${rule.minLength} characters long`);
      }
      if (rule.maxLength && value.length > rule.maxLength) {
        fieldErrors.push(`${field} must be no more than ${rule.maxLength} characters long`);
      }
    }

    // Numeric range validation
    if (typeof value === 'number') {
      if (rule.min !== undefined && value < rule.min) {
        fieldErrors.push(`${field} must be at least ${rule.min}`);
      }
      if (rule.max !== undefined && value > rule.max) {
        fieldErrors.push(`${field} must be no more than ${rule.max}`);
      }
    }

    // Pattern validation
    if (rule.pattern && typeof value === 'string') {
      if (!rule.pattern.test(value)) {
        fieldErrors.push(`${field} format is invalid`);
      }
    }

    // Custom validation
    if (rule.custom) {
      const customResult = rule.custom(value);
      if (customResult !== true) {
        fieldErrors.push(typeof customResult === 'string' ? customResult : `${field} is invalid`);
      }
    }

    // Sanitization
    let sanitizedValue = value;
    if (rule.sanitize && typeof value === 'string') {
      switch (rule.type) {
        case 'email':
          sanitizedValue = sanitize.email(value);
          break;
        case 'phone':
          sanitizedValue = sanitize.phone(value);
          break;
        default:
          sanitizedValue = sanitize.text(value);
          break;
      }
    }

    sanitizedData[field] = sanitizedValue;

    if (fieldErrors.length > 0) {
      errors[field] = fieldErrors;
    }
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors,
    sanitizedData
  };
}

// Predefined schemas for common entities
export const schemas = {
  user: {
    name: { required: true, type: 'string' as const, minLength: 2, maxLength: 100, sanitize: true },
    email: { required: true, type: 'email' as const, sanitize: true },
    password: { required: true, type: 'string' as const, custom: validators.strongPassword },
    role: { required: true, type: 'string' as const, pattern: /^(admin|manager|attendant)$/ },
    phone: { type: 'phone' as const, sanitize: true },
    isActive: { required: true, type: 'boolean' as const }
  },

  product: {
    name: { required: true, type: 'string' as const, minLength: 2, maxLength: 200, sanitize: true },
    description: { type: 'string' as const, maxLength: 1000, sanitize: true },
    category: { required: true, type: 'string' as const, sanitize: true },
    price: { required: true, type: 'number' as const, min: 0, custom: validators.currency },
    stockQuantity: { required: true, type: 'number' as const, custom: validators.positiveInteger },
    reorderLevel: { required: true, type: 'number' as const, custom: validators.positiveInteger },
    isActive: { required: true, type: 'boolean' as const },
    hasExpiry: { type: 'boolean' as const },
    expiryDate: { type: 'date' as const }
  },

  service: {
    name: { required: true, type: 'string' as const, minLength: 2, maxLength: 200, sanitize: true },
    description: { type: 'string' as const, maxLength: 1000, sanitize: true },
    category: { required: true, type: 'string' as const, sanitize: true },
    basePrice: { required: true, type: 'number' as const, min: 0, custom: validators.currency },
    isActive: { required: true, type: 'boolean' as const },
    allowCustomPricing: { type: 'boolean' as const },
    requiresNotes: { type: 'boolean' as const }
  },

  customer: {
    name: { required: true, type: 'string' as const, minLength: 2, maxLength: 100, sanitize: true },
    email: { required: true, type: 'email' as const, sanitize: true },
    phone: { required: true, type: 'phone' as const, sanitize: true },
    address: { type: 'string' as const, maxLength: 500, sanitize: true },
    notes: { type: 'string' as const, maxLength: 1000, sanitize: true },
    isActive: { required: true, type: 'boolean' as const }
  },

  transaction: {
    items: { required: true, type: 'array' as const },
    subtotal: { required: true, type: 'number' as const, min: 0, custom: validators.currency },
    total: { required: true, type: 'number' as const, min: 0, custom: validators.currency },
    paymentMethod: { required: true, type: 'string' as const, pattern: /^(cash|mpesa|debt)$/ },
    customerName: { type: 'string' as const, sanitize: true },
    attendantId: { required: true, type: 'string' as const },
    notes: { type: 'string' as const, maxLength: 500, sanitize: true }
  }
};

// Rate limiting utility
export class RateLimiter {
  private requests: Map<string, number[]> = new Map();

  isAllowed(identifier: string, maxRequests: number, windowMs: number): boolean {
    const now = Date.now();
    const windowStart = now - windowMs;
    
    if (!this.requests.has(identifier)) {
      this.requests.set(identifier, []);
    }
    
    const userRequests = this.requests.get(identifier)!;
    
    // Remove old requests outside the window
    const validRequests = userRequests.filter(time => time > windowStart);
    
    if (validRequests.length >= maxRequests) {
      return false;
    }
    
    validRequests.push(now);
    this.requests.set(identifier, validRequests);
    
    return true;
  }

  reset(identifier: string): void {
    this.requests.delete(identifier);
  }

  cleanup(): void {
    const now = Date.now();
    const oneHourAgo = now - 3600000; // 1 hour
    
    for (const [identifier, requests] of this.requests.entries()) {
      const validRequests = requests.filter(time => time > oneHourAgo);
      if (validRequests.length === 0) {
        this.requests.delete(identifier);
      } else {
        this.requests.set(identifier, validRequests);
      }
    }
  }
}

// Input sanitization middleware
export function sanitizeInput(input: any): any {
  if (typeof input === 'string') {
    return sanitize.text(input);
  }
  
  if (Array.isArray(input)) {
    return input.map(sanitizeInput);
  }
  
  if (typeof input === 'object' && input !== null) {
    const sanitized: any = {};
    for (const [key, value] of Object.entries(input)) {
      sanitized[key] = sanitizeInput(value);
    }
    return sanitized;
  }
  
  return input;
}

// Security headers utility
export function getSecurityHeaders(): Record<string, string> {
  return {
    'Content-Security-Policy': "default-src 'self'; script-src 'self' 'unsafe-inline' https://apis.google.com; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; connect-src 'self' https://firestore.googleapis.com https://firebase.googleapis.com;",
    'X-Content-Type-Options': 'nosniff',
    'X-Frame-Options': 'DENY',
    'X-XSS-Protection': '1; mode=block',
    'Referrer-Policy': 'strict-origin-when-cross-origin',
    'Permissions-Policy': 'camera=(), microphone=(), geolocation=()'
  };
}
