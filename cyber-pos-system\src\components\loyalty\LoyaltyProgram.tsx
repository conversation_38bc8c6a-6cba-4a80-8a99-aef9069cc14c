import React, { useState, useEffect } from 'react';
import {
  Star,
  Gift,
  Award,
  TrendingUp,
  Users,
  Calendar,
  Settings,
  Plus,
  Edit,
  Trash2,
  X,
  Save
} from 'lucide-react';

interface LoyaltyRule {
  id: string;
  name: string;
  description: string;
  pointsPerKsh: number;
  minimumSpend: number;
  isActive: boolean;
  createdAt: Date;
}

interface LoyaltyReward {
  id: string;
  name: string;
  description: string;
  pointsRequired: number;
  rewardType: 'discount' | 'freeItem' | 'cashback';
  rewardValue: number;
  isActive: boolean;
  expiryDays?: number;
  createdAt: Date;
}

interface LoyaltyTransaction {
  id: string;
  customerId: string;
  customerName: string;
  type: 'earned' | 'redeemed';
  points: number;
  description: string;
  transactionId?: string;
  createdAt: Date;
}

const LoyaltyProgram: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'overview' | 'rules' | 'rewards' | 'transactions'>('overview');
  const [loyaltyRules, setLoyaltyRules] = useState<LoyaltyRule[]>([]);
  const [loyaltyRewards, setLoyaltyRewards] = useState<LoyaltyReward[]>([]);
  const [loyaltyTransactions, setLoyaltyTransactions] = useState<LoyaltyTransaction[]>([]);
  const [showRuleForm, setShowRuleForm] = useState(false);
  const [showRewardForm, setShowRewardForm] = useState(false);
  const [editingRule, setEditingRule] = useState<LoyaltyRule | null>(null);
  const [editingReward, setEditingReward] = useState<LoyaltyReward | null>(null);

  // Mock data
  useEffect(() => {
    setLoyaltyRules([
      {
        id: '1',
        name: 'Standard Points',
        description: 'Earn 1 point for every KSh 100 spent',
        pointsPerKsh: 0.01,
        minimumSpend: 0,
        isActive: true,
        createdAt: new Date('2024-01-01')
      },
      {
        id: '2',
        name: 'Premium Bonus',
        description: 'Double points for purchases over KSh 1000',
        pointsPerKsh: 0.02,
        minimumSpend: 1000,
        isActive: true,
        createdAt: new Date('2024-01-01')
      }
    ]);

    setLoyaltyRewards([
      {
        id: '1',
        name: '10% Discount',
        description: '10% off your next purchase',
        pointsRequired: 100,
        rewardType: 'discount',
        rewardValue: 10,
        isActive: true,
        expiryDays: 30,
        createdAt: new Date('2024-01-01')
      },
      {
        id: '2',
        name: 'Free A4 Paper',
        description: 'Free pack of A4 paper (500 sheets)',
        pointsRequired: 50,
        rewardType: 'freeItem',
        rewardValue: 0,
        isActive: true,
        expiryDays: 60,
        createdAt: new Date('2024-01-01')
      },
      {
        id: '3',
        name: 'KSh 50 Cashback',
        description: 'KSh 50 credit to your account',
        pointsRequired: 200,
        rewardType: 'cashback',
        rewardValue: 50,
        isActive: true,
        createdAt: new Date('2024-01-01')
      }
    ]);

    setLoyaltyTransactions([
      {
        id: '1',
        customerId: '1',
        customerName: 'John Doe',
        type: 'earned',
        points: 25,
        description: 'Purchase - Transaction #TXN001',
        transactionId: 'TXN001',
        createdAt: new Date('2024-01-15')
      },
      {
        id: '2',
        customerId: '1',
        customerName: 'John Doe',
        type: 'redeemed',
        points: -50,
        description: 'Redeemed: Free A4 Paper',
        createdAt: new Date('2024-01-14')
      }
    ]);
  }, []);

  // Calculate loyalty statistics
  const loyaltyStats = {
    totalPointsEarned: loyaltyTransactions
      .filter(t => t.type === 'earned')
      .reduce((sum, t) => sum + t.points, 0),
    totalPointsRedeemed: Math.abs(loyaltyTransactions
      .filter(t => t.type === 'redeemed')
      .reduce((sum, t) => sum + t.points, 0)),
    activeCustomers: new Set(loyaltyTransactions.map(t => t.customerId)).size,
    redemptionRate: 0.25 // Mock calculation
  };

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-KE', {
      style: 'currency',
      currency: 'KES',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  // Format date
  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-KE', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Loyalty Program</h2>
          <p className="text-gray-600">Manage customer loyalty points and rewards</p>
        </div>
        <div className="flex space-x-2">
          <button
            onClick={() => setShowRuleForm(true)}
            className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 flex items-center text-sm"
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Rule
          </button>
          <button
            onClick={() => setShowRewardForm(true)}
            className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 flex items-center text-sm"
          >
            <Gift className="h-4 w-4 mr-2" />
            Add Reward
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <Star className="h-8 w-8 text-yellow-600" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">Points Earned</p>
              <p className="text-2xl font-bold text-gray-900">{loyaltyStats.totalPointsEarned}</p>
            </div>
          </div>
        </div>
        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <Gift className="h-8 w-8 text-green-600" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">Points Redeemed</p>
              <p className="text-2xl font-bold text-gray-900">{loyaltyStats.totalPointsRedeemed}</p>
            </div>
          </div>
        </div>
        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <Users className="h-8 w-8 text-blue-600" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">Active Members</p>
              <p className="text-2xl font-bold text-gray-900">{loyaltyStats.activeCustomers}</p>
            </div>
          </div>
        </div>
        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <TrendingUp className="h-8 w-8 text-purple-600" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">Redemption Rate</p>
              <p className="text-2xl font-bold text-gray-900">{(loyaltyStats.redemptionRate * 100).toFixed(1)}%</p>
            </div>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="bg-white shadow rounded-lg">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8 px-6">
            {[
              { id: 'overview', name: 'Overview', icon: TrendingUp },
              { id: 'rules', name: 'Point Rules', icon: Settings },
              { id: 'rewards', name: 'Rewards', icon: Gift },
              { id: 'transactions', name: 'Transactions', icon: Calendar }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <tab.icon className="h-4 w-4 mr-2" />
                {tab.name}
              </button>
            ))}
          </nav>
        </div>

        <div className="p-6">
          {activeTab === 'overview' && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Active Rules */}
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Active Point Rules</h3>
                  <div className="space-y-3">
                    {loyaltyRules.filter(rule => rule.isActive).map((rule) => (
                      <div key={rule.id} className="border border-gray-200 rounded-lg p-4">
                        <h4 className="font-medium text-gray-900">{rule.name}</h4>
                        <p className="text-sm text-gray-600 mt-1">{rule.description}</p>
                        <div className="mt-2 text-sm">
                          <span className="text-blue-600 font-medium">
                            {rule.pointsPerKsh} points per KSh
                          </span>
                          {rule.minimumSpend > 0 && (
                            <span className="text-gray-500 ml-2">
                              (min. {formatCurrency(rule.minimumSpend)})
                            </span>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Popular Rewards */}
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Available Rewards</h3>
                  <div className="space-y-3">
                    {loyaltyRewards.filter(reward => reward.isActive).slice(0, 3).map((reward) => (
                      <div key={reward.id} className="border border-gray-200 rounded-lg p-4">
                        <div className="flex items-center justify-between">
                          <div>
                            <h4 className="font-medium text-gray-900">{reward.name}</h4>
                            <p className="text-sm text-gray-600 mt-1">{reward.description}</p>
                          </div>
                          <div className="text-right">
                            <div className="text-lg font-bold text-blue-600">{reward.pointsRequired}</div>
                            <div className="text-xs text-gray-500">points</div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              {/* Recent Transactions */}
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Activity</h3>
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Customer</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Type</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Points</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Description</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Date</th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {loyaltyTransactions.slice(0, 5).map((transaction) => (
                        <tr key={transaction.id}>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            {transaction.customerName}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                              transaction.type === 'earned' 
                                ? 'bg-green-100 text-green-800' 
                                : 'bg-red-100 text-red-800'
                            }`}>
                              {transaction.type === 'earned' ? 'Earned' : 'Redeemed'}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            <span className={transaction.type === 'earned' ? 'text-green-600' : 'text-red-600'}>
                              {transaction.type === 'earned' ? '+' : ''}{transaction.points}
                            </span>
                          </td>
                          <td className="px-6 py-4 text-sm text-gray-500">
                            {transaction.description}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {formatDate(transaction.createdAt)}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'rules' && (
            <div>
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-semibold text-gray-900">Point Rules</h3>
                <button
                  onClick={() => setShowRuleForm(true)}
                  className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 flex items-center text-sm"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add Rule
                </button>
              </div>

              <div className="space-y-4">
                {loyaltyRules.map((rule) => (
                  <div key={rule.id} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="flex items-center">
                          <h4 className="font-medium text-gray-900">{rule.name}</h4>
                          <span className={`ml-2 inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                            rule.isActive ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                          }`}>
                            {rule.isActive ? 'Active' : 'Inactive'}
                          </span>
                        </div>
                        <p className="text-sm text-gray-600 mt-1">{rule.description}</p>
                        <div className="mt-2 text-sm">
                          <span className="text-blue-600 font-medium">
                            {rule.pointsPerKsh} points per KSh
                          </span>
                          {rule.minimumSpend > 0 && (
                            <span className="text-gray-500 ml-2">
                              (minimum spend: {formatCurrency(rule.minimumSpend)})
                            </span>
                          )}
                        </div>
                      </div>
                      <div className="flex space-x-2">
                        <button
                          onClick={() => {
                            setEditingRule(rule);
                            setShowRuleForm(true);
                          }}
                          className="text-blue-600 hover:text-blue-900"
                        >
                          <Edit className="h-4 w-4" />
                        </button>
                        <button className="text-red-600 hover:text-red-900">
                          <Trash2 className="h-4 w-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {activeTab === 'rewards' && (
            <div>
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-semibold text-gray-900">Rewards</h3>
                <button
                  onClick={() => setShowRewardForm(true)}
                  className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 flex items-center text-sm"
                >
                  <Gift className="h-4 w-4 mr-2" />
                  Add Reward
                </button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {loyaltyRewards.map((reward) => (
                  <div key={reward.id} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center">
                          <Gift className="h-5 w-5 text-green-600 mr-2" />
                          <h4 className="font-medium text-gray-900">{reward.name}</h4>
                        </div>
                        <p className="text-sm text-gray-600 mt-1">{reward.description}</p>
                        <div className="mt-3">
                          <div className="text-lg font-bold text-blue-600">{reward.pointsRequired} points</div>
                          {reward.rewardType === 'discount' && (
                            <div className="text-sm text-gray-500">{reward.rewardValue}% discount</div>
                          )}
                          {reward.rewardType === 'cashback' && (
                            <div className="text-sm text-gray-500">{formatCurrency(reward.rewardValue)} cashback</div>
                          )}
                          {reward.expiryDays && (
                            <div className="text-xs text-gray-400 mt-1">Expires in {reward.expiryDays} days</div>
                          )}
                        </div>
                      </div>
                      <div className="flex flex-col space-y-1">
                        <button
                          onClick={() => {
                            setEditingReward(reward);
                            setShowRewardForm(true);
                          }}
                          className="text-blue-600 hover:text-blue-900"
                        >
                          <Edit className="h-4 w-4" />
                        </button>
                        <button className="text-red-600 hover:text-red-900">
                          <Trash2 className="h-4 w-4" />
                        </button>
                      </div>
                    </div>
                    <div className="mt-3">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        reward.isActive ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                      }`}>
                        {reward.isActive ? 'Active' : 'Inactive'}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {activeTab === 'transactions' && (
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-6">Loyalty Transactions</h3>
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Customer</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Type</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Points</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Description</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Date</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {loyaltyTransactions.map((transaction) => (
                      <tr key={transaction.id}>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                          {transaction.customerName}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                            transaction.type === 'earned' 
                              ? 'bg-green-100 text-green-800' 
                              : 'bg-red-100 text-red-800'
                          }`}>
                            {transaction.type === 'earned' ? 'Earned' : 'Redeemed'}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          <span className={transaction.type === 'earned' ? 'text-green-600' : 'text-red-600'}>
                            {transaction.type === 'earned' ? '+' : ''}{transaction.points}
                          </span>
                        </td>
                        <td className="px-6 py-4 text-sm text-gray-500">
                          {transaction.description}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {formatDate(transaction.createdAt)}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default LoyaltyProgram;
