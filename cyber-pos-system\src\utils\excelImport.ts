import * as XLSX from 'xlsx';
import { Product } from '../types';

export interface ImportResult {
  success: boolean;
  data?: Partial<Product>[];
  errors?: string[];
  warnings?: string[];
}

export interface ImportValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

// Required columns for product import
const REQUIRED_COLUMNS = ['Name', 'Category', 'Price', 'Stock Quantity'];

// Optional columns with their default values
const OPTIONAL_COLUMNS = {
  'Description': '',
  'Reorder Level': 10,
  'Has Expiry': false,
  'Expiry Date': null,
  'Is Active': true
};

// Column mapping for different naming conventions
const COLUMN_MAPPINGS: { [key: string]: string } = {
  'product name': 'Name',
  'product_name': 'Name',
  'productname': 'Name',
  'item name': 'Name',
  'item_name': 'Name',
  'itemname': 'Name',
  'title': 'Name',
  
  'product category': 'Category',
  'product_category': 'Category',
  'productcategory': 'Category',
  'item category': 'Category',
  'item_category': 'Category',
  'itemcategory': 'Category',
  'cat': 'Category',
  
  'unit price': 'Price',
  'unit_price': 'Price',
  'unitprice': 'Price',
  'cost': 'Price',
  'amount': 'Price',
  
  'stock': 'Stock Quantity',
  'quantity': 'Stock Quantity',
  'qty': 'Stock Quantity',
  'inventory': 'Stock Quantity',
  'stock_quantity': 'Stock Quantity',
  'stockquantity': 'Stock Quantity',
  
  'desc': 'Description',
  'product description': 'Description',
  'product_description': 'Description',
  'productdescription': 'Description',
  'item description': 'Description',
  'item_description': 'Description',
  'itemdescription': 'Description',
  
  'reorder': 'Reorder Level',
  'reorder_level': 'Reorder Level',
  'reorderlevel': 'Reorder Level',
  'min stock': 'Reorder Level',
  'min_stock': 'Reorder Level',
  'minstock': 'Reorder Level',
  'minimum': 'Reorder Level',
  
  'expiry': 'Has Expiry',
  'has_expiry': 'Has Expiry',
  'hasexpiry': 'Has Expiry',
  'expires': 'Has Expiry',
  'perishable': 'Has Expiry',
  
  'expiry_date': 'Expiry Date',
  'expirydate': 'Expiry Date',
  'expiration': 'Expiry Date',
  'expiration_date': 'Expiry Date',
  'expirationdate': 'Expiry Date',
  'expires_on': 'Expiry Date',
  'expireson': 'Expiry Date',
  
  'active': 'Is Active',
  'is_active': 'Is Active',
  'isactive': 'Is Active',
  'enabled': 'Is Active',
  'status': 'Is Active'
};

/**
 * Normalize column headers to match expected format
 */
function normalizeHeaders(headers: string[]): string[] {
  return headers.map(header => {
    const normalized = header.toLowerCase().trim();
    return COLUMN_MAPPINGS[normalized] || header;
  });
}

/**
 * Validate that required columns are present
 */
function validateHeaders(headers: string[]): ImportValidationResult {
  const normalizedHeaders = normalizeHeaders(headers);
  const errors: string[] = [];
  const warnings: string[] = [];
  
  // Check for required columns
  const missingRequired = REQUIRED_COLUMNS.filter(
    required => !normalizedHeaders.includes(required)
  );
  
  if (missingRequired.length > 0) {
    errors.push(`Missing required columns: ${missingRequired.join(', ')}`);
  }
  
  // Check for duplicate columns
  const duplicates = normalizedHeaders.filter(
    (header, index) => normalizedHeaders.indexOf(header) !== index
  );
  
  if (duplicates.length > 0) {
    warnings.push(`Duplicate columns found: ${duplicates.join(', ')}`);
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
}

/**
 * Parse and validate a single row of product data
 */
function parseProductRow(
  rowData: { [key: string]: any }, 
  rowIndex: number
): { product: Partial<Product> | null; errors: string[] } {
  const errors: string[] = [];
  const product: Partial<Product> = {};
  
  try {
    // Name (required)
    const name = String(rowData['Name'] || '').trim();
    if (!name) {
      errors.push(`Row ${rowIndex}: Name is required`);
    } else {
      product.name = name;
    }
    
    // Category (required)
    const category = String(rowData['Category'] || '').trim();
    if (!category) {
      errors.push(`Row ${rowIndex}: Category is required`);
    } else {
      product.category = category;
    }
    
    // Price (required)
    const priceValue = rowData['Price'];
    if (priceValue === undefined || priceValue === null || priceValue === '') {
      errors.push(`Row ${rowIndex}: Price is required`);
    } else {
      const price = parseFloat(String(priceValue).replace(/[^\d.-]/g, ''));
      if (isNaN(price) || price < 0) {
        errors.push(`Row ${rowIndex}: Price must be a valid positive number`);
      } else {
        product.price = price;
      }
    }
    
    // Stock Quantity (required)
    const stockValue = rowData['Stock Quantity'];
    if (stockValue === undefined || stockValue === null || stockValue === '') {
      errors.push(`Row ${rowIndex}: Stock Quantity is required`);
    } else {
      const stockQuantity = parseInt(String(stockValue));
      if (isNaN(stockQuantity) || stockQuantity < 0) {
        errors.push(`Row ${rowIndex}: Stock Quantity must be a valid non-negative integer`);
      } else {
        product.stockQuantity = stockQuantity;
      }
    }
    
    // Description (optional)
    const description = String(rowData['Description'] || '').trim();
    product.description = description;
    
    // Reorder Level (optional)
    const reorderValue = rowData['Reorder Level'];
    if (reorderValue !== undefined && reorderValue !== null && reorderValue !== '') {
      const reorderLevel = parseInt(String(reorderValue));
      if (isNaN(reorderLevel) || reorderLevel < 0) {
        errors.push(`Row ${rowIndex}: Reorder Level must be a valid non-negative integer`);
      } else {
        product.reorderLevel = reorderLevel;
      }
    } else {
      product.reorderLevel = 10; // Default value
    }
    
    // Has Expiry (optional)
    const hasExpiryValue = rowData['Has Expiry'];
    if (hasExpiryValue !== undefined && hasExpiryValue !== null && hasExpiryValue !== '') {
      const hasExpiryStr = String(hasExpiryValue).toLowerCase().trim();
      product.hasExpiry = ['true', '1', 'yes', 'y'].includes(hasExpiryStr);
    } else {
      product.hasExpiry = false;
    }
    
    // Expiry Date (optional)
    const expiryDateValue = rowData['Expiry Date'];
    if (expiryDateValue && product.hasExpiry) {
      try {
        let expiryDate: Date;
        
        if (typeof expiryDateValue === 'number') {
          // Excel date serial number
          expiryDate = XLSX.SSF.parse_date_code(expiryDateValue);
        } else {
          // String date
          expiryDate = new Date(String(expiryDateValue));
        }
        
        if (isNaN(expiryDate.getTime())) {
          errors.push(`Row ${rowIndex}: Invalid expiry date format`);
        } else {
          product.expiryDate = expiryDate;
        }
      } catch (error) {
        errors.push(`Row ${rowIndex}: Invalid expiry date format`);
      }
    }
    
    // Is Active (optional)
    const isActiveValue = rowData['Is Active'];
    if (isActiveValue !== undefined && isActiveValue !== null && isActiveValue !== '') {
      const isActiveStr = String(isActiveValue).toLowerCase().trim();
      product.isActive = !['false', '0', 'no', 'n', 'inactive', 'disabled'].includes(isActiveStr);
    } else {
      product.isActive = true; // Default value
    }
    
    return { product: errors.length === 0 ? product : null, errors };

  } catch (error) {
    errors.push(`Row ${rowIndex}: Error parsing data - ${error instanceof Error ? error.message : 'Unknown error'}`);
    return { product: null, errors };
  }
}

/**
 * Parse Excel file and extract product data
 */
export async function parseExcelFile(file: File): Promise<ImportResult> {
  try {
    const arrayBuffer = await file.arrayBuffer();
    const workbook = XLSX.read(arrayBuffer, { type: 'array' });

    // Use the first worksheet
    const firstSheetName = workbook.SheetNames[0];
    if (!firstSheetName) {
      return {
        success: false,
        errors: ['No worksheets found in the Excel file']
      };
    }

    const worksheet = workbook.Sheets[firstSheetName];

    // Convert to JSON with header row
    const jsonData = XLSX.utils.sheet_to_json(worksheet, {
      header: 1,
      defval: '',
      blankrows: false
    }) as any[][];

    if (jsonData.length === 0) {
      return {
        success: false,
        errors: ['The Excel file appears to be empty']
      };
    }

    // Extract headers from first row
    const headers = jsonData[0].map(header => String(header).trim());
    const normalizedHeaders = normalizeHeaders(headers);

    // Validate headers
    const headerValidation = validateHeaders(headers);
    if (!headerValidation.isValid) {
      return {
        success: false,
        errors: headerValidation.errors,
        warnings: headerValidation.warnings
      };
    }

    // Process data rows
    const products: Partial<Product>[] = [];
    const errors: string[] = [...headerValidation.errors];
    const warnings: string[] = [...headerValidation.warnings];

    for (let i = 1; i < jsonData.length; i++) {
      const row = jsonData[i];

      // Skip empty rows
      if (row.every(cell => !cell || String(cell).trim() === '')) {
        continue;
      }

      // Create row object with normalized headers
      const rowData: { [key: string]: any } = {};
      normalizedHeaders.forEach((header, index) => {
        rowData[header] = row[index];
      });

      // Parse and validate the row
      const { product, errors: rowErrors } = parseProductRow(rowData, i + 1);

      if (product) {
        products.push(product);
      }

      errors.push(...rowErrors);
    }

    return {
      success: errors.length === 0,
      data: products,
      errors: errors.length > 0 ? errors : undefined,
      warnings: warnings.length > 0 ? warnings : undefined
    };

  } catch (error) {
    return {
      success: false,
      errors: [`Failed to parse Excel file: ${error instanceof Error ? error.message : 'Unknown error'}`]
    };
  }
}

/**
 * Parse CSV file and extract product data
 */
export async function parseCSVFile(file: File): Promise<ImportResult> {
  try {
    const text = await file.text();
    const lines = text.split('\n').filter(line => line.trim());

    if (lines.length === 0) {
      return {
        success: false,
        errors: ['The CSV file appears to be empty']
      };
    }

    // Parse CSV headers
    const headers = lines[0].split(',').map(h => h.replace(/"/g, '').trim());
    const normalizedHeaders = normalizeHeaders(headers);

    // Validate headers
    const headerValidation = validateHeaders(headers);
    if (!headerValidation.isValid) {
      return {
        success: false,
        errors: headerValidation.errors,
        warnings: headerValidation.warnings
      };
    }

    // Process data rows
    const products: Partial<Product>[] = [];
    const errors: string[] = [...headerValidation.errors];
    const warnings: string[] = [...headerValidation.warnings];

    for (let i = 1; i < lines.length; i++) {
      const line = lines[i].trim();
      if (!line) continue;

      // Simple CSV parsing (handles quoted values)
      const values = line.split(',').map(v => v.replace(/"/g, '').trim());

      // Create row object with normalized headers
      const rowData: { [key: string]: any } = {};
      normalizedHeaders.forEach((header, index) => {
        rowData[header] = values[index] || '';
      });

      // Parse and validate the row
      const { product, errors: rowErrors } = parseProductRow(rowData, i + 1);

      if (product) {
        products.push(product);
      }

      errors.push(...rowErrors);
    }

    return {
      success: errors.length === 0,
      data: products,
      errors: errors.length > 0 ? errors : undefined,
      warnings: warnings.length > 0 ? warnings : undefined
    };

  } catch (error) {
    return {
      success: false,
      errors: [`Failed to parse CSV file: ${error instanceof Error ? error.message : 'Unknown error'}`]
    };
  }
}

/**
 * Main function to parse import file (Excel or CSV)
 */
export async function parseImportFile(file: File): Promise<ImportResult> {
  const fileName = file.name.toLowerCase();

  if (fileName.endsWith('.xlsx') || fileName.endsWith('.xls')) {
    return parseExcelFile(file);
  } else if (fileName.endsWith('.csv')) {
    return parseCSVFile(file);
  } else {
    return {
      success: false,
      errors: ['Unsupported file format. Please use Excel (.xlsx, .xls) or CSV (.csv) files.']
    };
  }
}

/**
 * Generate a sample Excel template for product import
 */
export function generateImportTemplate(): void {
  const headers = [
    'Name',
    'Description',
    'Category',
    'Price',
    'Stock Quantity',
    'Reorder Level',
    'Has Expiry',
    'Expiry Date',
    'Is Active'
  ];

  const sampleData = [
    [
      'A4 Paper (Ream)',
      '500 sheets of A4 paper',
      'Paper',
      450,
      25,
      10,
      'No',
      '',
      'Yes'
    ],
    [
      'Blue Pen',
      'Ballpoint pen - blue ink',
      'Writing',
      20,
      50,
      20,
      'No',
      '',
      'Yes'
    ],
    [
      'Printer Ink Cartridge',
      'Black ink cartridge for HP printers',
      'Printer',
      1200,
      5,
      3,
      'Yes',
      '2025-12-31',
      'Yes'
    ]
  ];

  // Create workbook and worksheet
  const workbook = XLSX.utils.book_new();
  const worksheet = XLSX.utils.aoa_to_sheet([headers, ...sampleData]);

  // Add worksheet to workbook
  XLSX.utils.book_append_sheet(workbook, worksheet, 'Products');

  // Generate and download file
  XLSX.writeFile(workbook, `product-import-template-${new Date().toISOString().split('T')[0]}.xlsx`);
}
