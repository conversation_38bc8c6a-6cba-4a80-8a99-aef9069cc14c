# Excel Import Guide

This guide explains how to import product data from Excel and CSV files into the Cyber POS system.

## Supported File Formats

- **Excel files**: `.xlsx`, `.xls`
- **CSV files**: `.csv`

## Required Columns

The following columns are required in your import file:

| Column Name | Description | Example |
|-------------|-------------|---------|
| Name | Product name | "A4 Paper (Ream)" |
| Category | Product category | "Paper" |
| Price | Product price (in KSh) | 450 |
| Stock Quantity | Current stock quantity | 25 |

## Optional Columns

These columns are optional and will use default values if not provided:

| Column Name | Description | Default Value | Example |
|-------------|-------------|---------------|---------|
| Description | Product description | Empty string | "500 sheets of A4 paper" |
| Reorder Level | Minimum stock level | 10 | 15 |
| Has Expiry | Whether product expires | false | "Yes", "No", "true", "false" |
| Expiry Date | Product expiry date | null | "2025-12-31" |
| Is Active | Whether product is active | true | "Yes", "No", "true", "false" |

## Column Name Variations

The system recognizes various column name formats. These are all equivalent:

### Product Name
- Name, Product Name, Product_Name, Item Name, Title

### Category
- Category, Product Category, Item Category, Cat

### Price
- Price, Unit Price, Cost, Amount

### Stock Quantity
- Stock Quantity, Stock, Quantity, Qty, Inventory

### Description
- Description, Desc, Product Description, Item Description

### Reorder Level
- Reorder Level, Reorder, Min Stock, Minimum

### Has Expiry
- Has Expiry, Expiry, Expires, Perishable

### Expiry Date
- Expiry Date, Expiration, Expiration Date, Expires On

### Is Active
- Is Active, Active, Enabled, Status

## Data Format Guidelines

### Prices
- Use numeric values only (no currency symbols)
- Decimal values are supported (e.g., 450.50)
- Negative values are not allowed

### Stock Quantities
- Use whole numbers only
- Negative values are not allowed

### Boolean Fields (Has Expiry, Is Active)
- **True values**: "Yes", "Y", "True", "1", "Active", "Enabled"
- **False values**: "No", "N", "False", "0", "Inactive", "Disabled"

### Dates (Expiry Date)
- Use standard date formats: "YYYY-MM-DD", "MM/DD/YYYY", "DD/MM/YYYY"
- Excel date values are automatically converted

## Sample Excel Template

You can download a sample template by clicking the "Download Template" button in the import dialog. The template includes:

```
Name                    | Category | Price | Stock Quantity | Description                      | Reorder Level | Has Expiry | Expiry Date | Is Active
A4 Paper (Ream)        | Paper    | 450   | 25             | 500 sheets of A4 paper          | 10            | No         |             | Yes
Blue Pen               | Writing  | 20    | 50             | Ballpoint pen - blue ink         | 20            | No         |             | Yes
Printer Ink Cartridge  | Printer  | 1200  | 5              | Black ink cartridge for HP       | 3             | Yes        | 2025-12-31  | Yes
```

## Import Process

1. **Access Import**: Go to Inventory → Select products → Bulk Operations → Import
2. **Download Template** (optional): Click "Download Template" to get a sample file
3. **Prepare Your File**: Create your Excel or CSV file with the required columns
4. **Select File**: Choose your file using the file picker
5. **Preview**: Review the parsed data in the preview table
6. **Confirm Import**: Click "Confirm Import" to add the products to your inventory

## Error Handling

The system will validate your data and show errors for:

- Missing required columns
- Invalid data types (non-numeric prices, non-integer stock quantities)
- Missing required values
- Invalid date formats

## Tips for Successful Import

1. **Use the template**: Download and modify the provided template
2. **Check data types**: Ensure prices are numbers and stock quantities are whole numbers
3. **Consistent formatting**: Use consistent date and boolean formats
4. **Remove empty rows**: Delete any completely empty rows from your file
5. **Test with small batches**: Try importing a few products first to verify the format

## Troubleshooting

### Common Issues

**"Missing required columns" error**
- Ensure your file has Name, Category, Price, and Stock Quantity columns
- Check for typos in column headers

**"Invalid price/stock quantity" errors**
- Remove currency symbols from price columns
- Ensure stock quantities are whole numbers
- Check for empty cells in required columns

**"File parsing failed" error**
- Ensure the file is a valid Excel (.xlsx, .xls) or CSV (.csv) file
- Try saving the file in a different format
- Check that the file is not corrupted

**Products not appearing after import**
- Check that "Is Active" is set to true/yes for the products
- Verify the products were actually created by checking the inventory list

## Support

If you encounter issues with the import functionality, please check:
1. File format compatibility
2. Required column presence
3. Data type validation
4. Console logs for detailed error messages

For additional support, contact your system administrator.
