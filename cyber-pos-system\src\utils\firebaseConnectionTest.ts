import { collection, getDocs, query, limit } from 'firebase/firestore';
import { db } from '../config/firebase';

/**
 * Test Firebase Firestore connection and basic operations
 */
export const testFirebaseConnection = async (): Promise<{
  success: boolean;
  message: string;
  details?: any;
}> => {
  try {
    console.log('🔍 Testing Firebase connection...');
    
    // Test 1: Basic connection by trying to read from a collection
    const testQuery = query(collection(db, 'services'), limit(1));
    const snapshot = await getDocs(testQuery);
    
    console.log('✅ Firebase connection successful');
    console.log(`📊 Found ${snapshot.size} documents in services collection`);
    
    return {
      success: true,
      message: `Firebase connection successful. Found ${snapshot.size} services.`,
      details: {
        documentsFound: snapshot.size,
        connectionTime: new Date().toISOString()
      }
    };
  } catch (error: any) {
    console.error('❌ Firebase connection failed:', error);
    
    let message = 'Firebase connection failed';
    if (error.code === 'failed-precondition' && error.message.includes('index')) {
      message = 'Database index is being created. Please wait a few minutes.';
    } else if (error.code === 'permission-denied') {
      message = 'Permission denied. Please check Firebase security rules.';
    } else if (error.code === 'unavailable') {
      message = 'Firebase service is temporarily unavailable.';
    } else {
      message = `Firebase error: ${error.message}`;
    }
    
    return {
      success: false,
      message,
      details: {
        errorCode: error.code,
        errorMessage: error.message,
        timestamp: new Date().toISOString()
      }
    };
  }
};

/**
 * Test services query specifically
 */
export const testServicesQuery = async (): Promise<{
  success: boolean;
  message: string;
  servicesCount?: number;
}> => {
  try {
    console.log('🔍 Testing services query...');
    
    // Test the exact query that was failing
    const servicesQuery = query(
      collection(db, 'services'),
      // Note: We'll test without orderBy first to see if the collection exists
      limit(10)
    );
    
    const snapshot = await getDocs(servicesQuery);
    
    console.log(`✅ Services query successful - found ${snapshot.size} services`);
    
    return {
      success: true,
      message: `Services query successful. Found ${snapshot.size} services.`,
      servicesCount: snapshot.size
    };
  } catch (error: any) {
    console.error('❌ Services query failed:', error);
    
    return {
      success: false,
      message: `Services query failed: ${error.message}`,
      servicesCount: 0
    };
  }
};
