rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Helper functions for role-based access control
    function isAuthenticated() {
      return request.auth != null;
    }
    
    function getUserRole() {
      return get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role;
    }
    
    function isAdmin() {
      return isAuthenticated() && getUserRole() == 'admin';
    }
    
    function isManager() {
      return isAuthenticated() && (getUserRole() == 'manager' || getUserRole() == 'admin');
    }
    
    function isAttendant() {
      return isAuthenticated() && (getUserRole() == 'attendant' || getUserRole() == 'manager' || getUserRole() == 'admin');
    }
    
    function isOwner(userId) {
      return isAuthenticated() && request.auth.uid == userId;
    }
    
    function isValidUser(userData) {
      return userData.keys().hasAll(['name', 'email', 'role', 'isActive', 'createdAt', 'updatedAt']) &&
             userData.name is string &&
             userData.email is string &&
             userData.role in ['admin', 'manager', 'attendant'] &&
             userData.isActive is bool &&
             userData.createdAt is timestamp &&
             userData.updatedAt is timestamp;
    }
    
    function isValidProduct(productData) {
      return productData.keys().hasAll(['name', 'category', 'price', 'stockQuantity', 'reorderLevel', 'isActive', 'createdAt', 'updatedAt']) &&
             productData.name is string &&
             productData.category is string &&
             productData.price is number &&
             productData.price >= 0 &&
             productData.stockQuantity is int &&
             productData.stockQuantity >= 0 &&
             productData.reorderLevel is int &&
             productData.reorderLevel >= 0 &&
             productData.isActive is bool &&
             productData.createdAt is timestamp &&
             productData.updatedAt is timestamp;
    }
    
    function isValidService(serviceData) {
      return serviceData.keys().hasAll(['name', 'category', 'basePrice', 'isActive', 'createdAt', 'updatedAt']) &&
             serviceData.name is string &&
             serviceData.category is string &&
             serviceData.basePrice is number &&
             serviceData.basePrice >= 0 &&
             serviceData.isActive is bool &&
             serviceData.createdAt is timestamp &&
             serviceData.updatedAt is timestamp;
    }
    
    function isValidTransaction(transactionData) {
      return transactionData.keys().hasAll(['items', 'subtotal', 'total', 'paymentMethod', 'attendantId', 'createdAt']) &&
             transactionData.items is list &&
             transactionData.items.size() > 0 &&
             transactionData.subtotal is number &&
             transactionData.subtotal >= 0 &&
             transactionData.total is number &&
             transactionData.total >= 0 &&
             transactionData.paymentMethod in ['cash', 'mpesa', 'debt'] &&
             transactionData.attendantId is string &&
             transactionData.createdAt is timestamp;
    }
    
    // Users collection
    match /users/{userId} {
      // Users can read their own data, managers and admins can read all users
      allow read: if isOwner(userId) || isManager();
      
      // Only admins can create new users
      allow create: if isAdmin() && isValidUser(resource.data);
      
      // Users can update their own profile (limited fields), admins can update any user
      allow update: if (isOwner(userId) && 
                       onlyUpdating(['name', 'updatedAt']) &&
                       isValidUser(resource.data)) ||
                      (isAdmin() && isValidUser(resource.data));
      
      // Only admins can delete users
      allow delete: if isAdmin();
    }
    
    // Products collection
    match /products/{productId} {
      // All authenticated users can read products
      allow read: if isAuthenticated();
      
      // Only managers and admins can create/update/delete products
      allow create: if isManager() && isValidProduct(resource.data);
      allow update: if isManager() && isValidProduct(resource.data);
      allow delete: if isManager();
    }
    
    // Services collection
    match /services/{serviceId} {
      // All authenticated users can read services
      allow read: if isAuthenticated();
      
      // Only managers and admins can create/update/delete services
      allow create: if isManager() && isValidService(resource.data);
      allow update: if isManager() && isValidService(resource.data);
      allow delete: if isManager();
    }
    
    // Transactions collection
    match /transactions/{transactionId} {
      // All authenticated users can read transactions
      allow read: if isAuthenticated();
      
      // All authenticated users can create transactions (for POS)
      allow create: if isAttendant() && 
                   isValidTransaction(resource.data) &&
                   resource.data.attendantId == request.auth.uid;
      
      // Only managers and admins can update transactions
      allow update: if isManager() && isValidTransaction(resource.data);
      
      // Only admins can delete transactions
      allow delete: if isAdmin();
    }
    
    // Customers collection
    match /customers/{customerId} {
      // All authenticated users can read customers
      allow read: if isAuthenticated();
      
      // All authenticated users can create/update customers
      allow create: if isAttendant();
      allow update: if isAttendant();
      
      // Only managers and admins can delete customers
      allow delete: if isManager();
    }
    
    // Suppliers collection
    match /suppliers/{supplierId} {
      // All authenticated users can read suppliers
      allow read: if isAuthenticated();
      
      // Only managers and admins can create/update/delete suppliers
      allow create: if isManager();
      allow update: if isManager();
      allow delete: if isManager();
    }
    
    // Loyalty transactions collection
    match /loyaltyTransactions/{transactionId} {
      // All authenticated users can read loyalty transactions
      allow read: if isAuthenticated();
      
      // All authenticated users can create loyalty transactions
      allow create: if isAttendant();
      
      // Only managers and admins can update/delete loyalty transactions
      allow update: if isManager();
      allow delete: if isManager();
    }
    
    // System settings collection
    match /settings/{settingId} {
      // All authenticated users can read settings
      allow read: if isAuthenticated();
      
      // Only admins can create/update/delete settings
      allow create: if isAdmin();
      allow update: if isAdmin();
      allow delete: if isAdmin();
    }
    
    // Reports collection (for cached reports)
    match /reports/{reportId} {
      // Only managers and admins can read reports
      allow read: if isManager();
      
      // Only managers and admins can create/update reports
      allow create: if isManager();
      allow update: if isManager();
      
      // Only admins can delete reports
      allow delete: if isAdmin();
    }
    
    // Audit logs collection
    match /auditLogs/{logId} {
      // Only admins can read audit logs
      allow read: if isAdmin();
      
      // System can create audit logs (server-side only)
      allow create: if false; // Only server-side functions can create audit logs
      
      // No updates or deletes allowed
      allow update: if false;
      allow delete: if false;
    }
    
    // Helper function to check if only specific fields are being updated
    function onlyUpdating(fields) {
      return request.resource.data.diff(resource.data).affectedKeys().hasOnly(fields);
    }
    
    // Deny all other access
    match /{document=**} {
      allow read, write: if false;
    }
  }
}
