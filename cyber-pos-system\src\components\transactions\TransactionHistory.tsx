import React, { useState, useEffect } from 'react';
import {
  Search,
  Filter,
  Calendar,
  Download,
  Eye,
  Receipt,
  RefreshCw,
  ChevronDown,
  DollarSign,
  CreditCard,
  Smartphone,
  User
} from 'lucide-react';
import { useTransactionHistory, TransactionFilters } from '../../hooks/useTransactionHistory';
import { useAuth } from '../../contexts/AuthContext';
import { printReceipt } from '../../utils/receiptGenerator';

const TransactionHistory: React.FC = () => {
  const { hasPermission, currentUser } = useAuth();
  const {
    transactions,
    loading,
    error,
    hasMore,
    fetchTransactions,
    getTransactionById,
    loadMoreTransactions,
    searchTransactions,
    getTransactionStats,
    refreshTransactions
  } = useTransactionHistory();

  const [searchTerm, setSearchTerm] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState<TransactionFilters>({});
  const [selectedTransaction, setSelectedTransaction] = useState<string | null>(null);

  // Load initial transactions
  useEffect(() => {
    fetchTransactions();
  }, []);

  // Handle search
  const handleSearch = (term: string) => {
    setSearchTerm(term);
    if (term.trim()) {
      searchTransactions(term, filters);
    } else {
      refreshTransactions(filters);
    }
  };

  // Handle filter changes
  const handleFilterChange = (newFilters: Partial<TransactionFilters>) => {
    const updatedFilters = { ...filters, ...newFilters };
    setFilters(updatedFilters);
    fetchTransactions(updatedFilters);
  };

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-KE', {
      style: 'currency',
      currency: 'KES',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  // Format date
  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-KE', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).format(date);
  };

  // Get payment method icon
  const getPaymentMethodIcon = (method: string) => {
    switch (method) {
      case 'cash':
        return <DollarSign className="h-4 w-4" />;
      case 'mpesa':
        return <Smartphone className="h-4 w-4" />;
      case 'debt':
        return <User className="h-4 w-4" />;
      default:
        return <CreditCard className="h-4 w-4" />;
    }
  };

  // Get payment method color
  const getPaymentMethodColor = (method: string) => {
    switch (method) {
      case 'cash':
        return 'bg-green-100 text-green-800';
      case 'mpesa':
        return 'bg-blue-100 text-blue-800';
      case 'debt':
        return 'bg-orange-100 text-orange-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Reprint receipt
  const handleReprintReceipt = async (transactionId: string) => {
    try {
      const transaction = await getTransactionById(transactionId);
      if (transaction && currentUser) {
        const receiptData = {
          transaction,
          attendant: currentUser,
          businessInfo: {
            name: 'Cyber Services & Stationery',
            address: '123 Main Street, Nairobi',
            phone: '+254 700 123 456',
            email: '<EMAIL>',
          },
        };
        printReceipt(receiptData);
      }
    } catch (error) {
      console.error('Error reprinting receipt:', error);
      alert('Error reprinting receipt');
    }
  };

  const stats = getTransactionStats();

  if (!hasPermission(['admin', 'attendant'])) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-500">You don't have permission to view transaction history.</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex items-center justify-between mb-4">
          <h1 className="text-2xl font-bold text-gray-900">Transaction History</h1>
          <button
            onClick={() => refreshTransactions(filters)}
            className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </button>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <div className="bg-blue-50 p-4 rounded-lg">
            <div className="flex items-center">
              <DollarSign className="h-8 w-8 text-blue-600" />
              <div className="ml-3">
                <p className="text-sm font-medium text-blue-600">Total Amount</p>
                <p className="text-lg font-semibold text-blue-900">{formatCurrency(stats.totalAmount)}</p>
              </div>
            </div>
          </div>
          <div className="bg-green-50 p-4 rounded-lg">
            <div className="flex items-center">
              <Receipt className="h-8 w-8 text-green-600" />
              <div className="ml-3">
                <p className="text-sm font-medium text-green-600">Total Transactions</p>
                <p className="text-lg font-semibold text-green-900">{stats.totalCount}</p>
              </div>
            </div>
          </div>
          <div className="bg-purple-50 p-4 rounded-lg">
            <div className="flex items-center">
              <CreditCard className="h-8 w-8 text-purple-600" />
              <div className="ml-3">
                <p className="text-sm font-medium text-purple-600">Average Amount</p>
                <p className="text-lg font-semibold text-purple-900">{formatCurrency(stats.averageAmount)}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Search and Filters */}
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <input
                type="text"
                placeholder="Search transactions..."
                value={searchTerm}
                onChange={(e) => handleSearch(e.target.value)}
                className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500"
              />
            </div>
          </div>
          <button
            onClick={() => setShowFilters(!showFilters)}
            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
          >
            <Filter className="h-4 w-4 mr-2" />
            Filters
            <ChevronDown className="h-4 w-4 ml-2" />
          </button>
        </div>

        {/* Filter Panel */}
        {showFilters && (
          <div className="mt-4 p-4 bg-gray-50 rounded-lg">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Date From</label>
                <input
                  type="date"
                  value={filters.dateFrom ? filters.dateFrom.toISOString().split('T')[0] : ''}
                  onChange={(e) => handleFilterChange({ 
                    dateFrom: e.target.value ? new Date(e.target.value) : undefined 
                  })}
                  className="w-full border border-gray-300 rounded-md px-3 py-2"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Date To</label>
                <input
                  type="date"
                  value={filters.dateTo ? filters.dateTo.toISOString().split('T')[0] : ''}
                  onChange={(e) => handleFilterChange({ 
                    dateTo: e.target.value ? new Date(e.target.value) : undefined 
                  })}
                  className="w-full border border-gray-300 rounded-md px-3 py-2"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Payment Method</label>
                <select
                  value={filters.paymentMethod || 'all'}
                  onChange={(e) => handleFilterChange({ 
                    paymentMethod: e.target.value === 'all' ? undefined : e.target.value 
                  })}
                  className="w-full border border-gray-300 rounded-md px-3 py-2"
                >
                  <option value="all">All Methods</option>
                  <option value="cash">Cash</option>
                  <option value="mpesa">M-PESA</option>
                  <option value="debt">Credit/Debt</option>
                </select>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Transaction List */}
      <div className="bg-white shadow rounded-lg overflow-hidden">
        {loading && transactions.length === 0 ? (
          <div className="p-6 text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto"></div>
            <p className="mt-2 text-gray-500">Loading transactions...</p>
          </div>
        ) : error ? (
          <div className="p-6 text-center">
            <p className="text-red-600">Error: {error}</p>
          </div>
        ) : transactions.length === 0 ? (
          <div className="p-6 text-center">
            <Receipt className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No transactions found</h3>
            <p className="mt-1 text-sm text-gray-500">
              {searchTerm || Object.keys(filters).length > 0
                ? 'Try adjusting your search or filter criteria.'
                : 'No transactions have been recorded yet.'}
            </p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Transaction ID
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Date & Time
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Customer
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Items
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Payment
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Total
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Attendant
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {transactions.map((transaction) => (
                  <tr key={transaction.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      #{transaction.id.substring(0, 8)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {formatDate(transaction.createdAt)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {transaction.customerName || 'Walk-in Customer'}
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-500">
                      <div className="max-w-xs">
                        {transaction.items.slice(0, 2).map((item, index) => (
                          <div key={index} className="truncate">
                            {item.quantity}x {item.name}
                          </div>
                        ))}
                        {transaction.items.length > 2 && (
                          <div className="text-xs text-gray-400">
                            +{transaction.items.length - 2} more items
                          </div>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getPaymentMethodColor(transaction.paymentMethod)}`}>
                        {getPaymentMethodIcon(transaction.paymentMethod)}
                        <span className="ml-1 capitalize">{transaction.paymentMethod}</span>
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {formatCurrency(transaction.total)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {transaction.attendantName || 'Unknown'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex space-x-2">
                        <button
                          onClick={() => setSelectedTransaction(transaction.id)}
                          className="text-primary-600 hover:text-primary-900"
                          title="View Details"
                        >
                          <Eye className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => handleReprintReceipt(transaction.id)}
                          className="text-green-600 hover:text-green-900"
                          title="Reprint Receipt"
                        >
                          <Receipt className="h-4 w-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}

        {/* Load More Button */}
        {hasMore && transactions.length > 0 && (
          <div className="px-6 py-4 border-t border-gray-200">
            <button
              onClick={() => loadMoreTransactions(filters)}
              disabled={loading}
              className="w-full text-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
            >
              {loading ? 'Loading...' : 'Load More Transactions'}
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default TransactionHistory;
