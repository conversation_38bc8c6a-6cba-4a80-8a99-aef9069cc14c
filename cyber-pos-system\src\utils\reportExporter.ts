import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';
import { SalesReport } from '../hooks/useReports';
import { Product } from '../types';

export interface ExportOptions {
  format: 'pdf' | 'csv' | 'excel';
  includeCharts: boolean;
  dateRange: string;
  reportType: 'sales' | 'inventory' | 'services' | 'comprehensive';
}

// Export sales report to PDF
export const exportSalesReportToPDF = async (
  salesReport: SalesReport,
  options: Partial<ExportOptions> = {}
): Promise<void> => {
  const pdf = new jsPDF();
  const pageWidth = pdf.internal.pageSize.getWidth();
  const pageHeight = pdf.internal.pageSize.getHeight();
  let yPosition = 20;

  // Header
  pdf.setFontSize(20);
  pdf.setFont('helvetica', 'bold');
  pdf.text('Sales Report', pageWidth / 2, yPosition, { align: 'center' });
  yPosition += 15;

  pdf.setFontSize(12);
  pdf.setFont('helvetica', 'normal');
  pdf.text(`Period: ${salesReport.period}`, pageWidth / 2, yPosition, { align: 'center' });
  yPosition += 10;

  pdf.text(`Generated: ${new Date().toLocaleDateString()}`, pageWidth / 2, yPosition, { align: 'center' });
  yPosition += 20;

  // Sales Metrics
  pdf.setFontSize(16);
  pdf.setFont('helvetica', 'bold');
  pdf.text('Sales Summary', 20, yPosition);
  yPosition += 15;

  pdf.setFontSize(12);
  pdf.setFont('helvetica', 'normal');
  
  const metrics = [
    ['Total Sales:', `KSh ${salesReport.metrics.totalSales.toLocaleString()}`],
    ['Total Transactions:', salesReport.metrics.totalTransactions.toString()],
    ['Average Transaction:', `KSh ${salesReport.metrics.averageTransactionValue.toLocaleString()}`],
    ['Cash Sales:', `KSh ${salesReport.metrics.cashSales.toLocaleString()}`],
    ['M-PESA Sales:', `KSh ${salesReport.metrics.mpesaSales.toLocaleString()}`],
    ['Credit Sales:', `KSh ${salesReport.metrics.creditSales.toLocaleString()}`],
  ];

  metrics.forEach(([label, value]) => {
    pdf.text(label, 20, yPosition);
    pdf.text(value, 120, yPosition);
    yPosition += 8;
  });

  yPosition += 10;

  // Top Products
  if (salesReport.topProducts.length > 0) {
    pdf.setFontSize(16);
    pdf.setFont('helvetica', 'bold');
    pdf.text('Top Products', 20, yPosition);
    yPosition += 15;

    pdf.setFontSize(10);
    pdf.setFont('helvetica', 'bold');
    pdf.text('Product', 20, yPosition);
    pdf.text('Quantity', 100, yPosition);
    pdf.text('Revenue', 140, yPosition);
    yPosition += 8;

    pdf.setFont('helvetica', 'normal');
    salesReport.topProducts.slice(0, 10).forEach((product) => {
      if (yPosition > pageHeight - 30) {
        pdf.addPage();
        yPosition = 20;
      }
      
      pdf.text(product.name.substring(0, 30), 20, yPosition);
      pdf.text(product.quantitySold.toString(), 100, yPosition);
      pdf.text(`KSh ${product.revenue.toLocaleString()}`, 140, yPosition);
      yPosition += 6;
    });
  }

  // Top Services
  if (salesReport.topServices.length > 0) {
    yPosition += 10;
    
    if (yPosition > pageHeight - 50) {
      pdf.addPage();
      yPosition = 20;
    }

    pdf.setFontSize(16);
    pdf.setFont('helvetica', 'bold');
    pdf.text('Top Services', 20, yPosition);
    yPosition += 15;

    pdf.setFontSize(10);
    pdf.setFont('helvetica', 'bold');
    pdf.text('Service', 20, yPosition);
    pdf.text('Times Sold', 100, yPosition);
    pdf.text('Revenue', 140, yPosition);
    yPosition += 8;

    pdf.setFont('helvetica', 'normal');
    salesReport.topServices.slice(0, 10).forEach((service) => {
      if (yPosition > pageHeight - 30) {
        pdf.addPage();
        yPosition = 20;
      }
      
      pdf.text(service.name.substring(0, 30), 20, yPosition);
      pdf.text(service.timesSold.toString(), 100, yPosition);
      pdf.text(`KSh ${service.revenue.toLocaleString()}`, 140, yPosition);
      yPosition += 6;
    });
  }

  // Save the PDF
  const fileName = `sales-report-${salesReport.period}-${new Date().toISOString().split('T')[0]}.pdf`;
  pdf.save(fileName);
};

// Export data to CSV
export const exportToCSV = (
  data: any[],
  headers: string[],
  filename: string
): void => {
  const csvContent = [
    headers.join(','),
    ...data.map(row => 
      headers.map(header => {
        const value = row[header] || '';
        // Escape quotes and wrap in quotes if contains comma
        return typeof value === 'string' && value.includes(',') 
          ? `"${value.replace(/"/g, '""')}"` 
          : value;
      }).join(',')
    )
  ].join('\n');

  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');
  
  if (link.download !== undefined) {
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', filename);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }
};

// Export inventory report to CSV
export const exportInventoryToCSV = (products: Product[]): void => {
  const headers = [
    'id',
    'name',
    'category',
    'price',
    'stockQuantity',
    'reorderLevel',
    'isActive',
    'hasExpiry',
    'expiryDate',
    'createdAt'
  ];

  const data = products.map(product => ({
    id: product.id,
    name: product.name,
    category: product.category,
    price: product.price,
    stockQuantity: product.stockQuantity,
    reorderLevel: product.reorderLevel,
    isActive: product.isActive ? 'Yes' : 'No',
    hasExpiry: product.hasExpiry ? 'Yes' : 'No',
    expiryDate: product.expiryDate ? product.expiryDate.toISOString().split('T')[0] : '',
    createdAt: product.createdAt.toISOString().split('T')[0]
  }));

  const filename = `inventory-report-${new Date().toISOString().split('T')[0]}.csv`;
  exportToCSV(data, headers, filename);
};

// Export sales data to CSV
export const exportSalesDataToCSV = (salesReport: SalesReport): void => {
  // Export daily breakdown
  const dailyHeaders = ['date', 'sales', 'transactions'];
  const dailyData = salesReport.dailyBreakdown.map(day => ({
    date: day.date,
    sales: day.sales,
    transactions: day.transactions
  }));

  const dailyFilename = `daily-sales-${salesReport.period}-${new Date().toISOString().split('T')[0]}.csv`;
  exportToCSV(dailyData, dailyHeaders, dailyFilename);

  // Export top products
  if (salesReport.topProducts.length > 0) {
    const productHeaders = ['name', 'category', 'quantitySold', 'revenue'];
    const productData = salesReport.topProducts.map(product => ({
      name: product.name,
      category: product.category,
      quantitySold: product.quantitySold,
      revenue: product.revenue
    }));

    const productFilename = `top-products-${salesReport.period}-${new Date().toISOString().split('T')[0]}.csv`;
    exportToCSV(productData, productHeaders, productFilename);
  }

  // Export top services
  if (salesReport.topServices.length > 0) {
    const serviceHeaders = ['name', 'category', 'timesSold', 'revenue'];
    const serviceData = salesReport.topServices.map(service => ({
      name: service.name,
      category: service.category,
      timesSold: service.timesSold,
      revenue: service.revenue
    }));

    const serviceFilename = `top-services-${salesReport.period}-${new Date().toISOString().split('T')[0]}.csv`;
    exportToCSV(serviceData, serviceHeaders, serviceFilename);
  }
};

// Export chart as image
export const exportChartAsImage = async (
  chartElementId: string,
  filename: string
): Promise<void> => {
  const chartElement = document.getElementById(chartElementId);
  if (!chartElement) {
    throw new Error('Chart element not found');
  }

  try {
    const canvas = await html2canvas(chartElement, {
      backgroundColor: '#ffffff',
      scale: 2,
      useCORS: true
    });

    const link = document.createElement('a');
    link.download = filename;
    link.href = canvas.toDataURL('image/png');
    link.click();
  } catch (error) {
    console.error('Error exporting chart:', error);
    throw new Error('Failed to export chart');
  }
};

// Generate comprehensive business report
export const generateComprehensiveReport = async (
  salesReport: SalesReport,
  inventoryData: Product[],
  options: Partial<ExportOptions> = {}
): Promise<void> => {
  const pdf = new jsPDF();
  const pageWidth = pdf.internal.pageSize.getWidth();
  let yPosition = 20;

  // Cover Page
  pdf.setFontSize(24);
  pdf.setFont('helvetica', 'bold');
  pdf.text('Comprehensive Business Report', pageWidth / 2, yPosition, { align: 'center' });
  yPosition += 20;

  pdf.setFontSize(16);
  pdf.setFont('helvetica', 'normal');
  pdf.text('Cyber Services & Stationery', pageWidth / 2, yPosition, { align: 'center' });
  yPosition += 15;

  pdf.setFontSize(12);
  pdf.text(`Report Period: ${salesReport.period}`, pageWidth / 2, yPosition, { align: 'center' });
  yPosition += 10;
  pdf.text(`Generated: ${new Date().toLocaleDateString()}`, pageWidth / 2, yPosition, { align: 'center' });

  // Add new page for content
  pdf.addPage();
  yPosition = 20;

  // Executive Summary
  pdf.setFontSize(18);
  pdf.setFont('helvetica', 'bold');
  pdf.text('Executive Summary', 20, yPosition);
  yPosition += 15;

  pdf.setFontSize(12);
  pdf.setFont('helvetica', 'normal');
  
  const summaryText = [
    `Total Revenue: KSh ${salesReport.metrics.totalSales.toLocaleString()}`,
    `Total Transactions: ${salesReport.metrics.totalTransactions}`,
    `Average Transaction Value: KSh ${salesReport.metrics.averageTransactionValue.toLocaleString()}`,
    `Active Products: ${inventoryData.filter(p => p.isActive).length}`,
    `Low Stock Items: ${inventoryData.filter(p => p.stockQuantity <= p.reorderLevel).length}`,
  ];

  summaryText.forEach(text => {
    pdf.text(text, 20, yPosition);
    yPosition += 8;
  });

  // Key Insights
  yPosition += 15;
  pdf.setFontSize(16);
  pdf.setFont('helvetica', 'bold');
  pdf.text('Key Insights', 20, yPosition);
  yPosition += 15;

  pdf.setFontSize(12);
  pdf.setFont('helvetica', 'normal');
  
  const insights = [
    '• Cash remains the dominant payment method',
    '• Peak sales hours are between 12PM - 2PM',
    '• Stationery products show highest demand',
    '• Service bundling opportunities identified',
    '• Inventory optimization needed for fast-moving items'
  ];

  insights.forEach(insight => {
    pdf.text(insight, 20, yPosition);
    yPosition += 8;
  });

  // Save the comprehensive report
  const fileName = `comprehensive-report-${new Date().toISOString().split('T')[0]}.pdf`;
  pdf.save(fileName);
};

// Utility function to format currency for exports
export const formatCurrencyForExport = (amount: number): string => {
  return new Intl.NumberFormat('en-KE', {
    style: 'currency',
    currency: 'KES',
    minimumFractionDigits: 0,
  }).format(amount);
};

// Utility function to format date for exports
export const formatDateForExport = (date: Date): string => {
  return date.toLocaleDateString('en-KE', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  });
};
