import { useState, useEffect } from 'react';
import {
  collection,
  query,
  where,
  orderBy,
  limit,
  getDocs,
  Timestamp
} from 'firebase/firestore';
import { db } from '../config/firebase';
import { Transaction, Product } from '../types';

export interface DashboardStats {
  todaysSales: number;
  todaysTransactions: number;
  lowStockCount: number;
  activeCustomers: number;
  salesChange: number;
  transactionsChange: number;
}

export interface RecentTransaction {
  id: string;
  customerName: string;
  amount: number;
  time: string;
  type: string;
  items: string[];
}

export interface LowStockItem {
  id: string;
  name: string;
  current: number;
  minimum: number;
  category: string;
}

export const useDashboard = () => {
  const [stats, setStats] = useState<DashboardStats>({
    todaysSales: 0,
    todaysTransactions: 0,
    lowStockCount: 0,
    activeCustomers: 0,
    salesChange: 0,
    transactionsChange: 0,
  });
  const [recentTransactions, setRecentTransactions] = useState<RecentTransaction[]>([]);
  const [lowStockItems, setLowStockItems] = useState<LowStockItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Get date range for today
  const getTodayRange = () => {
    const today = new Date();
    const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
    const endOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate() + 1);
    return { startOfDay, endOfDay };
  };

  // Get date range for yesterday
  const getYesterdayRange = () => {
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    const startOfDay = new Date(yesterday.getFullYear(), yesterday.getMonth(), yesterday.getDate());
    const endOfDay = new Date(yesterday.getFullYear(), yesterday.getMonth(), yesterday.getDate() + 1);
    return { startOfDay, endOfDay };
  };

  // Fetch today's transactions
  const fetchTodaysTransactions = async () => {
    try {
      const { startOfDay, endOfDay } = getTodayRange();
      const transactionsQuery = query(
        collection(db, 'transactions'),
        where('createdAt', '>=', Timestamp.fromDate(startOfDay)),
        where('createdAt', '<', Timestamp.fromDate(endOfDay)),
        orderBy('createdAt', 'desc')
      );

      const snapshot = await getDocs(transactionsQuery);
      const transactions: Transaction[] = [];
      
      snapshot.forEach((doc) => {
        const data = doc.data();
        transactions.push({
          id: doc.id,
          items: data.items || [],
          subtotal: data.subtotal || 0,
          discount: data.discount,
          total: data.total || 0,
          paymentMethod: data.paymentMethod || 'cash',
          customerName: data.customerName || 'Walk-in Customer',
          attendantId: data.attendantId || '',
          attendantName: data.attendantName || '',
          notes: data.notes,
          createdAt: data.createdAt?.toDate() || new Date(),
        });
      });

      return transactions;
    } catch (error) {
      console.error('Error fetching today\'s transactions:', error);
      return [];
    }
  };

  // Fetch yesterday's transactions for comparison
  const fetchYesterdaysTransactions = async () => {
    try {
      const { startOfDay, endOfDay } = getYesterdayRange();
      const transactionsQuery = query(
        collection(db, 'transactions'),
        where('createdAt', '>=', Timestamp.fromDate(startOfDay)),
        where('createdAt', '<', Timestamp.fromDate(endOfDay))
      );

      const snapshot = await getDocs(transactionsQuery);
      const transactions: Transaction[] = [];
      
      snapshot.forEach((doc) => {
        const data = doc.data();
        transactions.push({
          id: doc.id,
          items: data.items || [],
          subtotal: data.subtotal || 0,
          discount: data.discount,
          total: data.total || 0,
          paymentMethod: data.paymentMethod || 'cash',
          customerName: data.customerName || 'Walk-in Customer',
          attendantId: data.attendantId || '',
          attendantName: data.attendantName || '',
          notes: data.notes,
          createdAt: data.createdAt?.toDate() || new Date(),
        });
      });

      return transactions;
    } catch (error) {
      console.error('Error fetching yesterday\'s transactions:', error);
      return [];
    }
  };

  // Fetch recent transactions for display
  const fetchRecentTransactions = async () => {
    try {
      const recentQuery = query(
        collection(db, 'transactions'),
        orderBy('createdAt', 'desc'),
        limit(5)
      );

      const snapshot = await getDocs(recentQuery);
      const recent: RecentTransaction[] = [];

      snapshot.forEach((doc) => {
        const data = doc.data();
        const createdAt = data.createdAt?.toDate() || new Date();
        const timeAgo = getTimeAgo(createdAt);
        
        // Get primary service/product type
        const primaryType = data.items?.[0]?.type === 'service' ? 
          data.items[0].name : 
          'Stationery';

        recent.push({
          id: doc.id,
          customerName: data.customerName || 'Walk-in Customer',
          amount: data.total || 0,
          time: timeAgo,
          type: primaryType,
          items: data.items?.map((item: any) => item.name) || [],
        });
      });

      return recent;
    } catch (error) {
      console.error('Error fetching recent transactions:', error);
      return [];
    }
  };

  // Fetch low stock products
  const fetchLowStockProducts = async () => {
    try {
      const productsQuery = query(
        collection(db, 'products'),
        where('isActive', '==', true)
      );

      const snapshot = await getDocs(productsQuery);
      const lowStock: LowStockItem[] = [];

      snapshot.forEach((doc) => {
        const data = doc.data();
        const stockQuantity = data.stockQuantity || 0;
        const reorderLevel = data.reorderLevel || 0;

        if (stockQuantity <= reorderLevel) {
          lowStock.push({
            id: doc.id,
            name: data.name || '',
            current: stockQuantity,
            minimum: reorderLevel,
            category: data.category || '',
          });
        }
      });

      return lowStock.sort((a, b) => (a.current - a.minimum) - (b.current - b.minimum));
    } catch (error) {
      console.error('Error fetching low stock products:', error);
      return [];
    }
  };

  // Helper function to get time ago string
  const getTimeAgo = (date: Date): string => {
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));

    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes} minute${diffInMinutes > 1 ? 's' : ''} ago`;
    
    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `${diffInHours} hour${diffInHours > 1 ? 's' : ''} ago`;
    
    const diffInDays = Math.floor(diffInHours / 24);
    return `${diffInDays} day${diffInDays > 1 ? 's' : ''} ago`;
  };

  // Calculate percentage change
  const calculateChange = (current: number, previous: number): number => {
    if (previous === 0) return current > 0 ? 100 : 0;
    return Math.round(((current - previous) / previous) * 100);
  };

  // Load all dashboard data
  const loadDashboardData = async () => {
    setLoading(true);
    setError(null);

    try {
      const [
        todaysTransactions,
        yesterdaysTransactions,
        recentTxns,
        lowStockProducts
      ] = await Promise.all([
        fetchTodaysTransactions(),
        fetchYesterdaysTransactions(),
        fetchRecentTransactions(),
        fetchLowStockProducts()
      ]);

      // Calculate today's stats
      const todaysSales = todaysTransactions.reduce((sum, txn) => sum + txn.total, 0);
      const todaysCount = todaysTransactions.length;

      // Calculate yesterday's stats for comparison
      const yesterdaysSales = yesterdaysTransactions.reduce((sum, txn) => sum + txn.total, 0);
      const yesterdaysCount = yesterdaysTransactions.length;

      // Calculate changes
      const salesChange = calculateChange(todaysSales, yesterdaysSales);
      const transactionsChange = calculateChange(todaysCount, yesterdaysCount);

      // Get unique customers from recent transactions
      const uniqueCustomers = new Set(
        todaysTransactions
          .filter(txn => txn.customerName && txn.customerName !== 'Walk-in Customer')
          .map(txn => txn.customerName)
      );

      setStats({
        todaysSales,
        todaysTransactions: todaysCount,
        lowStockCount: lowStockProducts.length,
        activeCustomers: uniqueCustomers.size,
        salesChange,
        transactionsChange,
      });

      setRecentTransactions(recentTxns);
      setLowStockItems(lowStockProducts.slice(0, 5)); // Show top 5 low stock items

    } catch (error) {
      console.error('Error loading dashboard data:', error);
      setError('Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  };

  // Load data on component mount
  useEffect(() => {
    loadDashboardData();
  }, []);

  // Refresh data function
  const refreshData = () => {
    loadDashboardData();
  };

  return {
    stats,
    recentTransactions,
    lowStockItems,
    loading,
    error,
    refreshData,
  };
};
