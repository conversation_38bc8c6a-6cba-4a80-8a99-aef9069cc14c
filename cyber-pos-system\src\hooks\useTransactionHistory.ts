import { useState, useEffect } from 'react';
import {
  collection,
  query,
  where,
  orderBy,
  limit,
  getDocs,
  doc,
  getDoc,
  Timestamp,
  startAfter,
  QueryDocumentSnapshot
} from 'firebase/firestore';
import { db } from '../config/firebase';
import { Transaction, User } from '../types';

export interface TransactionWithDetails extends Transaction {
  attendantName?: string;
  customerName?: string;
}

export interface TransactionFilters {
  dateFrom?: Date;
  dateTo?: Date;
  paymentMethod?: string;
  attendantId?: string;
  customerId?: string;
  minAmount?: number;
  maxAmount?: number;
}

export const useTransactionHistory = () => {
  const [transactions, setTransactions] = useState<TransactionWithDetails[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [hasMore, setHasMore] = useState(true);
  const [lastDoc, setLastDoc] = useState<QueryDocumentSnapshot | null>(null);

  // Fetch transactions with pagination
  const fetchTransactions = async (
    filters: TransactionFilters = {},
    pageSize: number = 20,
    loadMore: boolean = false
  ) => {
    setLoading(true);
    setError(null);

    try {
      let transactionQuery = query(
        collection(db, 'transactions'),
        orderBy('createdAt', 'desc'),
        limit(pageSize)
      );

      // Apply date filters
      if (filters.dateFrom) {
        transactionQuery = query(
          transactionQuery,
          where('createdAt', '>=', Timestamp.fromDate(filters.dateFrom))
        );
      }

      if (filters.dateTo) {
        transactionQuery = query(
          transactionQuery,
          where('createdAt', '<=', Timestamp.fromDate(filters.dateTo))
        );
      }

      // Apply payment method filter
      if (filters.paymentMethod && filters.paymentMethod !== 'all') {
        transactionQuery = query(
          transactionQuery,
          where('paymentMethod', '==', filters.paymentMethod)
        );
      }

      // Apply attendant filter
      if (filters.attendantId) {
        transactionQuery = query(
          transactionQuery,
          where('attendantId', '==', filters.attendantId)
        );
      }

      // Apply customer filter
      if (filters.customerId) {
        transactionQuery = query(
          transactionQuery,
          where('customerId', '==', filters.customerId)
        );
      }

      // For pagination
      if (loadMore && lastDoc) {
        transactionQuery = query(transactionQuery, startAfter(lastDoc));
      }

      const snapshot = await getDocs(transactionQuery);
      const fetchedTransactions: TransactionWithDetails[] = [];

      for (const docSnapshot of snapshot.docs) {
        const data = docSnapshot.data();
        const transaction: TransactionWithDetails = {
          id: docSnapshot.id,
          items: data.items || [],
          subtotal: data.subtotal || 0,
          discount: data.discount,
          total: data.total || 0,
          paymentMethod: data.paymentMethod || 'cash',
          customerId: data.customerId,
          attendantId: data.attendantId || '',
          status: data.status || 'completed',
          notes: data.notes,
          createdAt: data.createdAt?.toDate() || new Date(),
          updatedAt: data.updatedAt?.toDate() || new Date(),
        };

        // Fetch attendant name
        if (data.attendantId) {
          try {
            const attendantDoc = await getDoc(doc(db, 'users', data.attendantId));
            if (attendantDoc.exists()) {
              transaction.attendantName = attendantDoc.data().name;
            }
          } catch (error) {
            console.error('Error fetching attendant:', error);
          }
        }

        // Fetch customer name for credit sales
        if (data.customerId) {
          try {
            const customerDoc = await getDoc(doc(db, 'customers', data.customerId));
            if (customerDoc.exists()) {
              transaction.customerName = customerDoc.data().name;
            }
          } catch (error) {
            console.error('Error fetching customer:', error);
          }
        }

        fetchedTransactions.push(transaction);
      }

      // Apply amount filters (client-side since Firestore doesn't support range queries on multiple fields)
      let filteredTransactions = fetchedTransactions;
      if (filters.minAmount !== undefined) {
        filteredTransactions = filteredTransactions.filter(t => t.total >= filters.minAmount!);
      }
      if (filters.maxAmount !== undefined) {
        filteredTransactions = filteredTransactions.filter(t => t.total <= filters.maxAmount!);
      }

      if (loadMore) {
        setTransactions(prev => [...prev, ...filteredTransactions]);
      } else {
        setTransactions(filteredTransactions);
      }

      // Update pagination state
      setHasMore(snapshot.docs.length === pageSize);
      setLastDoc(snapshot.docs[snapshot.docs.length - 1] || null);

    } catch (error) {
      console.error('Error fetching transactions:', error);
      setError('Failed to fetch transactions');
    } finally {
      setLoading(false);
    }
  };

  // Get transaction by ID
  const getTransactionById = async (transactionId: string): Promise<TransactionWithDetails | null> => {
    try {
      const transactionDoc = await getDoc(doc(db, 'transactions', transactionId));
      if (!transactionDoc.exists()) {
        return null;
      }

      const data = transactionDoc.data();
      const transaction: TransactionWithDetails = {
        id: transactionDoc.id,
        items: data.items || [],
        subtotal: data.subtotal || 0,
        discount: data.discount,
        total: data.total || 0,
        paymentMethod: data.paymentMethod || 'cash',
        customerId: data.customerId,
        attendantId: data.attendantId || '',
        status: data.status || 'completed',
        notes: data.notes,
        createdAt: data.createdAt?.toDate() || new Date(),
        updatedAt: data.updatedAt?.toDate() || new Date(),
      };

      // Fetch attendant name
      if (data.attendantId) {
        try {
          const attendantDoc = await getDoc(doc(db, 'users', data.attendantId));
          if (attendantDoc.exists()) {
            transaction.attendantName = attendantDoc.data().name;
          }
        } catch (error) {
          console.error('Error fetching attendant:', error);
        }
      }

      // Fetch customer name
      if (data.customerId) {
        try {
          const customerDoc = await getDoc(doc(db, 'customers', data.customerId));
          if (customerDoc.exists()) {
            transaction.customerName = customerDoc.data().name;
          }
        } catch (error) {
          console.error('Error fetching customer:', error);
        }
      }

      return transaction;
    } catch (error) {
      console.error('Error fetching transaction:', error);
      throw error;
    }
  };

  // Get transactions for a specific date range
  const getTransactionsByDateRange = async (startDate: Date, endDate: Date) => {
    const filters: TransactionFilters = {
      dateFrom: startDate,
      dateTo: endDate,
    };
    await fetchTransactions(filters, 1000); // Get more for reports
  };

  // Get today's transactions
  const getTodaysTransactions = async () => {
    const today = new Date();
    const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
    const endOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate() + 1);
    
    await getTransactionsByDateRange(startOfDay, endOfDay);
  };

  // Load more transactions (pagination)
  const loadMoreTransactions = async (filters: TransactionFilters = {}) => {
    if (!hasMore || loading) return;
    await fetchTransactions(filters, 20, true);
  };

  // Search transactions
  const searchTransactions = async (searchTerm: string, filters: TransactionFilters = {}) => {
    // For now, we'll fetch all transactions and filter client-side
    // In a production app, you might want to use Algolia or similar for full-text search
    await fetchTransactions(filters, 1000);
    
    if (searchTerm.trim()) {
      const filtered = transactions.filter(transaction => 
        transaction.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
        transaction.attendantName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        transaction.customerName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        transaction.notes?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        transaction.items.some(item => 
          item.name.toLowerCase().includes(searchTerm.toLowerCase())
        )
      );
      setTransactions(filtered);
    }
  };

  // Calculate transaction statistics
  const getTransactionStats = () => {
    const totalAmount = transactions.reduce((sum, t) => sum + t.total, 0);
    const totalCount = transactions.length;
    const averageAmount = totalCount > 0 ? totalAmount / totalCount : 0;

    const paymentMethodBreakdown = transactions.reduce((acc, t) => {
      acc[t.paymentMethod] = (acc[t.paymentMethod] || 0) + t.total;
      return acc;
    }, {} as Record<string, number>);

    return {
      totalAmount,
      totalCount,
      averageAmount,
      paymentMethodBreakdown,
    };
  };

  // Refresh transactions
  const refreshTransactions = (filters: TransactionFilters = {}) => {
    setLastDoc(null);
    setHasMore(true);
    fetchTransactions(filters);
  };

  return {
    transactions,
    loading,
    error,
    hasMore,
    fetchTransactions,
    getTransactionById,
    getTransactionsByDateRange,
    getTodaysTransactions,
    loadMoreTransactions,
    searchTransactions,
    getTransactionStats,
    refreshTransactions,
  };
};
