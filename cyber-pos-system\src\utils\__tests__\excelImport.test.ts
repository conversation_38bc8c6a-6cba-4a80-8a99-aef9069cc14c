import { parseCSVFile, parseExcelFile } from '../excelImport';

// Mock XLSX module
jest.mock('xlsx', () => ({
  read: jest.fn(),
  utils: {
    sheet_to_json: jest.fn(),
    book_new: jest.fn(),
    aoa_to_sheet: jest.fn(),
    book_append_sheet: jest.fn()
  },
  writeFile: jest.fn(),
  SSF: {
    parse_date_code: jest.fn()
  }
}));

// Helper function to create a mock File with text() method
const createMockFile = (content: string, filename: string, type: string = 'text/csv') => {
  const file = new File([content], filename, { type });
  // Add the text() method that returns the content
  (file as any).text = jest.fn().mockResolvedValue(content);
  return file;
};

describe('Excel Import Utilities', () => {
  describe('parseCSVFile', () => {
    it('should parse a valid CSV file with required columns', async () => {
      const csvContent = `Name,Category,Price,Stock Quantity
A4 Paper,Paper,450,25
Blue Pen,Writing,20,50`;

      const file = createMockFile(csvContent, 'products.csv');

      const result = await parseCSVFile(file);

      expect(result.success).toBe(true);
      expect(result.data).toHaveLength(2);
      expect(result.data?.[0]).toMatchObject({
        name: 'A4 Paper',
        category: 'Paper',
        price: 450,
        stockQuantity: 25
      });
    });

    it('should handle missing required columns', async () => {
      const csvContent = `Name,Category
A4 Paper,Paper
Blue Pen,Writing`;

      const file = createMockFile(csvContent, 'products.csv');

      const result = await parseCSVFile(file);

      expect(result.success).toBe(false);
      expect(result.errors).toContain('Missing required columns: Price, Stock Quantity');
    });

    it('should handle invalid data types', async () => {
      const csvContent = `Name,Category,Price,Stock Quantity
A4 Paper,Paper,invalid_price,25
Blue Pen,Writing,20,invalid_stock`;

      const file = createMockFile(csvContent, 'products.csv');

      const result = await parseCSVFile(file);

      expect(result.success).toBe(false);
      expect(result.errors).toContain('Row 2: Price must be a valid positive number');
      expect(result.errors).toContain('Row 3: Stock Quantity must be a valid non-negative integer');
    });

    it('should handle optional columns with defaults', async () => {
      const csvContent = `Name,Category,Price,Stock Quantity,Description,Reorder Level,Has Expiry,Is Active
A4 Paper,Paper,450,25,Premium paper,15,No,Yes
Blue Pen,Writing,20,50,,,Yes,No`;

      const file = createMockFile(csvContent, 'products.csv');

      const result = await parseCSVFile(file);

      expect(result.success).toBe(true);
      expect(result.data).toHaveLength(2);

      // First product with all fields
      expect(result.data?.[0]).toMatchObject({
        name: 'A4 Paper',
        category: 'Paper',
        price: 450,
        stockQuantity: 25,
        description: 'Premium paper',
        reorderLevel: 15,
        hasExpiry: false,
        isActive: true
      });

      // Second product with defaults
      expect(result.data?.[1]).toMatchObject({
        name: 'Blue Pen',
        category: 'Writing',
        price: 20,
        stockQuantity: 50,
        description: '',
        reorderLevel: 10, // Default value
        hasExpiry: true,
        isActive: false
      });
    });

    it('should skip empty rows', async () => {
      const csvContent = `Name,Category,Price,Stock Quantity
A4 Paper,Paper,450,25

Blue Pen,Writing,20,50`;

      const file = createMockFile(csvContent, 'products.csv');

      const result = await parseCSVFile(file);

      // Check if there are any errors first
      if (!result.success) {
        console.log('Errors:', result.errors);
      }

      expect(result.success).toBe(true);
      expect(result.data).toHaveLength(2);
    });

    it('should handle column name variations', async () => {
      const csvContent = `product name,item category,unit price,qty
A4 Paper,Paper,450,25
Blue Pen,Writing,20,50`;

      const file = createMockFile(csvContent, 'products.csv');

      const result = await parseCSVFile(file);

      expect(result.success).toBe(true);
      expect(result.data).toHaveLength(2);
      expect(result.data?.[0]).toMatchObject({
        name: 'A4 Paper',
        category: 'Paper',
        price: 450,
        stockQuantity: 25
      });
    });
  });

  describe('parseExcelFile', () => {
    it('should handle file parsing errors gracefully', async () => {
      const file = createMockFile('invalid content', 'products.xlsx', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');

      // Mock arrayBuffer method for Excel files
      (file as any).arrayBuffer = jest.fn().mockResolvedValue(new ArrayBuffer(0));

      const result = await parseExcelFile(file);

      // The function should handle the error gracefully
      expect(result.success).toBe(false);
      expect(result.errors).toBeDefined();
    });
  });
});
